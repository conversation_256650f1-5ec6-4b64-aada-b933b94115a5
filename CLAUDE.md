# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 6.18.7 financial reporting application called "キャッシュ体質図" (Cash Constitution Chart). The application allows users to input financial data and generate visual charts and PDF reports for business analysis.

## Development Commands

### Frontend Build
```bash
# Development build (includes build step)
npm run dev

# Production build
npm run prod

# Watch mode for development
npm run watch

# Clean build artifacts
npm run clean
```

### Backend Testing
```bash
# Run all tests
vendor/bin/phpunit

# Run specific test suite
vendor/bin/phpunit --testsuite=Feature
vendor/bin/phpunit --testsuite=Unit

# Run single test file
vendor/bin/phpunit tests/Feature/ExampleTest.php
```

### Frontend E2E Testing
```bash
# Navigate to e2e directory first
cd e2e

# Run Playwright tests
npm run test

# Run tests in specific browsers
npm run test:chromium
npm run test:firefox  
npm run test:webkit

# Run tests with interactive UI (accessible at http://localhost:9323)
npm run test:ui

# Run tests in headed mode (with browser window)
npm run test:headed

# Show test report (accessible at http://localhost:9323)
npm run report

# Serve existing report via HTTP server
npm run report:serve

# Clean test artifacts
npm run clean
```

### Laravel Artisan Commands
```bash
# Database migrations
php artisan migrate

# Database seeding
php artisan db:seed

# Generate application key
php artisan key:generate

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Queue workers (if using queues)
php artisan queue:work
```

### Docker Development
```bash
# Start services
docker-compose up -d

# Build containers
docker-compose build

# Rebuild containers (needed after port changes)
docker-compose down && docker-compose up -d --build

# View logs
docker-compose logs -f
```

### Google Cloud Deployment
```bash
# Deploy to Cloud Run (using mise)
mise run deploy

# Start Cloud SQL Auth Proxy
mise run run:sqlauthproxy
```

## Architecture Overview

### Frontend Architecture
- **Framework**: Vue.js 2.6 with Vuex for state management
- **UI Components**: Element UI with custom themes
- **Charts**: D3.js v5 for financial data visualization
- **Build Tool**: Laravel Mix with Webpack and Rollup
- **Main Entry Points**:
  - `resources/js/app.js` - Main user application
  - `resources/js/manage.js` - Admin management interface
  - `resources/js/login.js` - Authentication
  - `resources/js/manage_history.js` - History management

### Backend Architecture
- **Framework**: Laravel 6.18.7 with PHP 7.2+
- **Authentication**: Dual authentication system (web users + admin)
- **Database**: MySQL with Eloquent ORM
- **PDF Generation**: wkhtmltopdf via barryvdh/laravel-snappy
- **Logging**: Google Cloud Logging integration
- **Key Models**:
  - `User` - End users with theme relationships
  - `UserInputSheet` - Financial data input sheets with soft deletes
  - `Administrator` - Admin users
  - `MstInputSample` - Sample data templates
  - `MstTheme` - UI theme configurations

### Route Structure
- **Web Routes** (`/`): Main user interface with authentication
- **Admin Routes** (`/manage`): Administrative interface
- **API Routes** (`/api`): Limited API with IP filtering for user data

### Key Features
- **Financial Data Input**: Users create input sheets with financial data
- **Chart Generation**: Constitution and transition charts using D3.js
- **PDF Export**: Generate PDF reports from chart data
- **Theme System**: Customizable UI themes stored in database
- **User Management**: Admin interface for user and data management
- **History Tracking**: Version control for user input sheets

### Database Architecture
- **Users Table**: User authentication and profile data
- **User Input Sheets**: Financial data with JSON storage for account information
- **Master Tables**: Themes, input samples, and category data
- **Admin Tables**: Separate admin authentication system

### Security Features
- **IP Filtering**: Configurable IP allowlists for web and admin access
- **Dual Authentication**: Separate auth guards for users and admins
- **CSRF Protection**: Laravel's built-in CSRF middleware
- **Input Validation**: Form request validation throughout

### Environment Configuration
- **Database**: MySQL with optional read/write separation
- **Caching**: File-based caching (configurable)
- **Logging**: Configurable channels (file, stdout, Google Cloud)
- **IP Restrictions**: Environment-based IP allowlists
- **Port Forwarding**: Port 9323 forwarded for Playwright report server access from host

## Development Workflow

### Local Development Setup
1. Copy `.env.example` to `.env` and configure database
2. Run `composer install` for PHP dependencies
3. Run `npm install` for Node.js dependencies
4. Generate application key: `php artisan key:generate`
5. Run migrations: `php artisan migrate`
6. Seed database: `php artisan db:seed`
7. Build frontend: `npm run dev`

### Testing Strategy
- **Unit Tests**: Test individual components and utilities
- **Feature Tests**: Test HTTP endpoints and user workflows  
- **E2E Tests**: Playwright tests in `e2e/` directory for browser automation
- **Test Database**: Uses SQLite in-memory for fast testing
- **Browser Testing**: Configured for Chromium, Firefox, and WebKit

### Deployment
- **Target Platform**: Google Cloud Run
- **Container**: Docker with PHP-FPM and Nginx
- **Database**: Google Cloud SQL MySQL
- **Build Process**: Multi-stage Docker build with asset compilation