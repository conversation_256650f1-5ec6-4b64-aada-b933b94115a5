{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2", "barryvdh/laravel-snappy": "^0.4.6", "fideloper/proxy": "^4.0", "google/cloud-error-reporting": "^0.15.0", "google/cloud-logging": "^1.18", "h4cc/wkhtmltoimage-amd64": "0.12.x", "h4cc/wkhtmltopdf-amd64": "0.12.x", "laravel/framework": "6.18.7", "laravel/tinker": "^2.0"}, "require-dev": {"facade/ignition": "^1.4", "fzaninotto/faker": "^1.9.1", "laravel/homestead": "^10.2", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}