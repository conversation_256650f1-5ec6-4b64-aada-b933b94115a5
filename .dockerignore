# Version control
.git
.github/
.gitignore
.gitattributes

# Environment and configuration
.env*
.editorconfig
phpunit.xml
.phpunit.result.cache

# Dependencies (will be installed in container)
node_modules
vendor

# Storage and cache directories (will be created in container)
storage/framework/cache/*
storage/framework/sessions/*
storage/framework/views/*
storage/logs/*
bootstrap/cache/*
!storage/app/.gitkeep
!storage/framework/.gitkeep
!bootstrap/cache/.gitkeep

# Development tools and documentation
docker-compose*.yml
compose.yml
Dockerfile*
.devcontainer/
*.md
README.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
tests/
.phpunit.result.cache
coverage/

# Local development files
Vagrantfile
Homestead.json
Homestead.yaml
.vagrant/
after.sh
aliases

# Build artifacts and compiled assets (will be built in container)
public/css/
public/js/
public/fonts/vendor/
public/mix-manifest.json
public/hot

# npm and build logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Credentials and secrets (should use Cloud Run secrets instead)
credentials/
*.key
*.pem
*.p12

# Large files that aren't needed
*.zip
*.tar.gz
*.rar

# IDE workspace files
*.code-workspace
