services:
  app:
    build:
      context: .
      dockerfile: ./docker/php/Dockerfile.apache
    ports:
      - "${WEB_PORT:-8080}:8080"
    depends_on:
      - db
    environment:
      - TZ=Asia/Tokyo

      - APP_NAME=${APP_NAME}
      - APP_KEY=${APP_KEY}
      - APP_ENV=${APP_ENV:-production}
      - APP_DEBUG=${APP_DEBUG:-false}
      - APP_URL=${APP_URL}
      - VIEW_COMPILED_PATH=/tmp
      - CACHE_DRIVER=database
      - SESSION_DRIVER=database
      - SESSION_LIFETIME=120
      - MIX_APP_REPOSITORY='api'

      - LOG_CHANNEL=${LOG_CHANNEL}

      - DB_CONNECTION=mysql
      - DB_HOST=${DB_HOST:-db}
      - DB_PORT=3306
      - DB_DATABASE=${DB_DATABASE:-tgm}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_USERNAME=${DB_READ_USERNAME}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
      - DB_WRITE_USERNAME=${DB_WRITE_USERNAME}
      - DB_WRITE_PASSWORD=${DB_WRITE_PASSWORD}

      # Google Cloud SQL Auth Proxy configuration
      - GOOGLE_CLOUD_SQL_CONNECTION_NAME=${GOOGLE_CLOUD_SQL_CONNECTION_NAME}

      - ALLOW_IP_ADDRESS_WEB=${ALLOW_IP_ADDRESS_WEB}
      - ALLOW_IP_ADDRESS_MANAGE=${ALLOW_IP_ADDRESS_MANAGE}
    restart: unless-stopped

  db:
    build:
      context: .
      dockerfile_inline: |
        FROM mysql:8.0
        COPY ./docker/mysql/my.cnf /etc/mysql/conf.d/my.cnf
    environment:
      - TZ=Asia/Tokyo

      - MYSQL_DATABASE=${DB_DATABASE:-tgm}
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
    volumes:
      - vol_mysql:/var/lib/mysql
    restart: unless-stopped

volumes:
  vol_mysql:
    driver: local
