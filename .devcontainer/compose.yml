services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.ubuntu
      args:
        TAG: 24.04
    ports:
      - "9323:9323"
    environment:
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=${DB_PORT:-3306}
      # - DB_DATABASE=${DB_DATABASE:-tgm_dev}
      - DB_USERNAME=${DB_USERNAME:-tgm_user}
      - DB_PASSWORD=${DB_PASSWORD:-tgm_password}
      - DB_READ_USERNAME=${DB_READ_USERNAME:-tgm_user}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD:-tgm_password}
      - DB_WRITE_USERNAME=${DB_WRITE_USERNAME:-tgm_user}
      - DB_WRITE_PASSWORD=${DB_WRITE_PASSWORD:-tgm_password}
      - TZ=Asia/Tokyo
    env_file:
      - devcontainer.env
    volumes:
      - ..:/var/www/html:cached
      - ../docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
      - ../docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
      - ../docker/php/www.conf:/etc/php/7.2/fpm/pool.d/zzz-docker.conf

  web:
    image: nginx:1.28
    ports:
      - "${WEB_PORT:-8080}:80"
    depends_on:
      - app
    environment:
      - TZ=Asia/Tokyo
    env_file:
      - devcontainer.env
    volumes:
      - ..:/var/www/html
      - ../docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
  db:
    image: mysql:8.0
    ports:
      - "${DB_PORT:-3306}:3306"
    environment:
      - MYSQL_DATABASE=${DB_DATABASE:-tgm_dev}
      - MYSQL_USER=${DB_USERNAME:-tgm_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-tgm_password}
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD:-root_password}
      - TZ=Asia/Tokyo
    env_file:
      - devcontainer.env
    volumes:
      - vol_mysql_dev:/var/lib/mysql
      - ../docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf

volumes:
  vol_mysql_dev:
    driver: local
