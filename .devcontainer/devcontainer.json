// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
    "name": "TGM Dev Container",
    "dockerComposeFile": [
        "./compose.yml"
    ],
    "service": "app",
    "remoteUser": "vscode",
    "workspaceFolder": "/var/www/html",
    "forwardPorts": [
        9323
    ],
    "portsAttributes": {
        "9323": {
            "label": "Playwright Report Server",
            "onAutoForward": "notify"
        }
    },
    "mounts": [
        "type=bind,source=${localEnv:HOME}/.config/claude,target=/home/<USER>/.config/claude",
        "type=bind,source=${localEnv:HOME}/.gemini,target=/home/<USER>/.gemini",
        "type=bind,source=${localEnv:HOME}/.config/gh,target=/home/<USER>/.config/gh,readonly"
    ],
    "features": {
        "ghcr.io/devcontainers/features/github-cli:1": {},
        "ghcr.io/devcontainers-extra/features/fd:1": {},
        "ghcr.io/devcontainers-extra/features/ripgrep:1": {},
        "ghcr.io/dhoeric/features/hadolint:1": {},
        "ghcr.io/shyim/devcontainers-features/php:0": {
            "version": "7.2",
            "extensionsExtra": "xdebug,gd,mbstring"
        },
        "ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {
            "packages": "libxext6,fontconfig,libxrender1,xfonts-75dpi,xfonts-base"
        },
        "ghcr.io/devcontainers-extra/features/mise:1": {}
    },
    "remoteEnv": {
        "CLAUDE_CONFIG_DIR": "/home/<USER>/.config/claude",
        "LANG": "C.UTF-8",
        "LC_ALL": "C.UTF-8"
    },
    // "postAttachCommand": "for dir in vendor node_modules; do if [ -d \"$dir\" ]; then owner=$(stat -c '%U' \"$dir\"); group=$(stat -c '%G' \"$dir\"); if [ \"$owner\" != \"vscode\" ] || [ \"$group\" != \"vscode\" ]; then sudo chown -R vscode:vscode \"$dir\"; fi; fi; done",
    "dotfiles": {
        "repository": "poppen/dotfiles",
        "installCommand": "devcontainer/install.sh",
        "targetPath": "~/dotfiles"
    },
    "postCreateCommand": [
        // "echo 'eval \"$(mise activate bash)\"' >> ~/.bashrc",
        // "echo 'alias gst=\"git status -sb && git stash list\"' >> ~/.bash_aliases"
        // "echo alias ll=\\'ls -alF\\' >> ~/.bash_aliases"
    ],
    "postStartCommand": [
        "ln -s /var/www/html/.devcontainer/dotfiles/dotconfig/mise ~/.config"
    ],
    "customizations": {
        "vscode": {
            "extensions": [
                "editorconfig.editorconfig",
                "ms-azuretools.vscode-docker",
                "xdebug.php-debug",
                "bmewburn.vscode-intelephense-client",
                "onecentlin.laravel-extension-pack",
                "spywhere.guides",
                "streetsidesoftware.code-spell-checker",
                "mosapride.zenkaku",
                "mhutchie.git-graph",
                "donjayamanne.githistory",
                "Augment.vscode-augment",
                "exiasr.hadolint",
                "Vue.volar",
                "ms-playwright.playwright",
                "biomejs.biome"
            ]
        }
    }
}
