<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserInputSheet;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserInputSheetPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any user input sheets.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
        return true;
    }

    /**
     * Determine whether the user can view the user input sheet.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return mixed
     */
    public function view(User $user, UserInputSheet $userInputSheet)
    {
        return $user->id == $userInputSheet->user_id;
    }

    /**
     * Determine whether the user can create user input sheets.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
        return true;
    }

    /**
     * Determine whether the user can update the user input sheet.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return mixed
     */
    public function update(User $user, UserInputSheet $userInputSheet)
    {
        return $user->id == $userInputSheet->user_id;
    }

    /**
     * Determine whether the user can delete the user input sheet.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return mixed
     */
    public function delete(User $user, UserInputSheet $userInputSheet)
    {
        return $user->id == $userInputSheet->user_id;
    }

    /**
     * Determine whether the user can restore the user input sheet.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return mixed
     */
    public function restore(User $user, UserInputSheet $userInputSheet)
    {
        return $user->id == $userInputSheet->user_id;
    }

    /**
     * Determine whether the user can permanently delete the user input sheet.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return mixed
     */
    public function forceDelete(User $user, UserInputSheet $userInputSheet)
    {
        return false;
    }
}
