<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class PdfController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function constitution(Request $request)
    {
        $user = $request->user();
        $param = [
            'leftTitle1' => $request->leftTitle1,
            'leftTitle2' => $request->leftTitle2,
            'rightTitle' => $request->rightTitle,
            'svg' => $request->svg,
            'unit' => $request->unit,
            'companyName' => $user->show_pdf_company_name ? $user->company_name : '',
        ];
        $pdf = App::make('snappy.pdf.wrapper');
        $pdf->loadView('pdf.constitution', $param)
            ->setOption('encoding', 'utf-8');
        return $pdf->download("キャッシュ体質図.pdf")
            ->header("Content-Type", "application/force-download")
            // GAE マルチバイトコードを使うと Content-dispositionヘッダが取り除かれてしまう対策
            // RFC 6266 Mac safari対策 日本語版以外のクライアント OS や RFC への準拠を考慮する
            ->header("Content-disposition", "attachment; filename*=UTF-8''" . urlencode("キャッシュ体質図.pdf"));
        // return view('pdf.constitution', $param);
    }
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function transition(Request $request)
    {
        $user = $request->user();
        $data = json_decode($request->transition);
        foreach ($data as $key => $v) {
            $data->{$key} = preg_split('/<\/svg>/', $v, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
        }
        $param = [
            'data' => $data,
            'leftTitle' => $request->leftTitle,
            'rightTitle' => $request->rightTitle,
            'unit' => $request->unit,
            'companyName' => $user->show_pdf_company_name ? $user->company_name : '',
        ];
        $pdf = App::make('snappy.pdf.wrapper');
        $pdf->loadView('pdf.transition', $param)
            ->setOption('encoding', 'utf-8')
            ->setOption('orientation', 'Landscape');
        return $pdf->download("キャッシュ体質推移.pdf")
            ->header("Content-Type", "application/force-download")
            // GAE マルチバイトコードを使うと Content-dispositionヘッダが取り除かれてしまう対策
            // RFC 6266 Mac safari対策 日本語版以外のクライアント OS や RFC への準拠を考慮する
            ->header("Content-disposition", "attachment; filename*=UTF-8''" . urlencode("キャッシュ体質推移.pdf"));
        // return view('pdf.transition', $param);
    }
}
