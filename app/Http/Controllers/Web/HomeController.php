<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserInputSheet as UserInputSheetResource;
use App\Models\MstInputSample;
use App\Models\UserInputSheet;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        try {
            $user = $request->user();
            $sample = MstInputSample::find(1);
            $histories = UserInputSheetResource::collection(UserInputSheet::where('user_id', $user->id)->get());
            
            // Debug logging for troubleshooting (non-production only)
            if (config('app.debug')) {
                \Log::debug('HomeController init response', [
                    'user_id' => $user->id,
                    'user_account_id' => $user->account_id,
                    'sample_exists' => !is_null($sample),
                    'histories_count' => $histories->count()
                ]);
            }
            
            return [
                'success' => true,
                'sample' => $sample ? $sample->account_json : null,
                'histories' => $histories,
                'user' => $user,
            ];
        } catch (\Exception $e) {
            \Log::error('HomeController init failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                // Only include full trace in debug mode
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize application data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
