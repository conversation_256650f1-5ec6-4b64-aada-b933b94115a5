<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\UserInputSheet;
use Illuminate\Http\Request;
use App\Http\Resources\UserInputSheet as UserInputSheetResource;

class SheetController extends Controller
{
    protected $validator = [
        'groupingName' => 'nullable|string|max:255',
        'dataName' => 'nullable|string|max:255',
        'dataDate' => 'required|date',
    ];

    public function __construct()
    {
        $this->authorizeResource(UserInputSheet::class, 'sheet');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate($this->validator);

        $user = $request->user();

        $userInputSheet = new UserInputSheet();
        $userInputSheet->user_id = $user->id;
        $userInputSheet->grouping_name = $request->groupingName ?: '';
        $userInputSheet->data_name = $request->dataName ?: '';
        $userInputSheet->data_date = $request->dataDate;
        $userInputSheet->capitalize_debt_from_president = $request->capitalizeDebtFromPresident;
        $userInputSheet->account_json = $request->accountJson ?: json_encode([]);
        $userInputSheet->filtered_account_json = $request->filteredAccountJson ?: json_encode([]);
        $userInputSheet->constitution_json = $request->constitutionJson ?: json_encode([]);
        $userInputSheet->disable_chart = $request->disableChart;
        $userInputSheet->save();

        $user->last_update_at = now();
        $user->save();

        return ['success' => true, 'userInputSheet' => new UserInputSheetResource($userInputSheet)];
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return \Illuminate\Http\Response
     */
    public function show(UserInputSheet $userInputSheet)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return \Illuminate\Http\Response
     */
    public function edit(UserInputSheet $userInputSheet)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UserInputSheet $userInputSheet)
    {
        $request->validate($this->validator);

        $userInputSheet->grouping_name = $request->groupingName ?: '';
        $userInputSheet->data_name = $request->dataName ?: '';
        $userInputSheet->data_date = $request->dataDate;
        $userInputSheet->capitalize_debt_from_president = $request->capitalizeDebtFromPresident;
        $userInputSheet->account_json = $request->accountJson ?: json_encode([]);
        $userInputSheet->filtered_account_json = $request->filteredAccountJson ?: json_encode([]);
        $userInputSheet->constitution_json = $request->constitutionJson ?: json_encode([]);
        $userInputSheet->disable_chart = $request->disableChart;
        $userInputSheet->save();

        $user = $request->user();
        $user->last_update_at = now();
        $user->save();

        return ['success' => true, 'userInputSheet' => new UserInputSheetResource($userInputSheet)];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\UserInputSheet  $userInputSheet
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, UserInputSheet $userInputSheet)
    {
        $user = $request->user();
        $user->last_update_at = now();
        $user->save();

        $userInputSheet->delete();
        return ['success' => true];
    }
}
