<?php

namespace App\Http\Controllers\Manage;

use App\Http\Controllers\Controller;
use App\Models\MstTheme;
use App\Models\User;
use App\Models\UserPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class UserContoroller extends Controller
{

    public function list(Request $request)
    {
        $users = User::orderBy('id', 'desc')->get();
        $search_list = [];
        foreach ($users as $key => $user) {
            $search_list[] = [
                "index" => $user->id,
                "word" => "{$user->company_name} || {$user->user_name} || {$user->account_id}",
            ];
            $password = UserPassword::where('user_id', $user->id)->first();
            if ($password) {
                $users[$key]->password = $password->password;
            } else {
                $users[$key]->password = '********';
            }
        }
        return view('manage.user.list', compact('users', 'search_list'));
    }

    public function create(Request $request)
    {
        $themes = MstTheme::all();
        return view('manage.user.create', compact('themes'));
    }

    public function edit(Request $request, $id)
    {
        $themes = MstTheme::all();
        $user = User::find($id);
        $password = UserPassword::where('user_id', $id)->first();
        if (!$user) {
            // TODO
            return redirect(route('manage.list'));
        }
        $item = $user->toArray();
        $item['password'] = '********';
        if ($password) $item['password'] = $password->password;
        $item['start_at'] = Carbon::parse($item['start_at'])->format('Y-m-d');
        $item['end_at'] = Carbon::parse($item['end_at'])->format('Y-m-d');
        return view('manage.user.edit', compact('item', 'themes'));
    }

    public function entry(Request $request)
    {
        $validatedData = $request->validate([
            'company_name' => ['required', 'string', 'max:255'],
            'user_name'    => ['required', 'string', 'max:255'],
            'account_id'   => ['required', 'string', 'max:255'],
            'password'     => ['required', 'string', 'max:16'],
            'start_at'     => ['required', 'date'],
            'end_at'       => ['required', 'date', 'after:start_at'],
            'mst_theme_id' => ['required', 'integer', 'exists:mst_themes,id'],
            'show_link'    => ['required', 'integer', 'max:1'],
        ]);

        $user_id = $request->input('user_id');
        if ("" == $user_id) {
            // 新規登録
            $user = new User();
            $password = new UserPassword();
            $request->validate(
                ['account_id' => [Rule::unique('users', 'account_id')->whereNull('deleted_at')]]
            );
        } else {
            // 編集
            $request->validate(['user_id' => ['exists:users,id']]);
            $user = User::find($user_id);
            $password = UserPassword::where('user_id', $user_id)->first();
            if (!$password) {
                $password = new UserPassword();
            }

            // ID変更してる場合は重複チェック
            if ($user->account_id != $request->input('account_id')) {
                $request->validate(
                    ['account_id' => [Rule::unique('users', 'account_id')->whereNull('deleted_at')]]
                );
            }
        }
        foreach ($validatedData as $key => $value) {
            if ('password' == $key) {
                if ("" == $user_id || '********' != $value) {
                    $user->$key = Hash::make($value);
                    $password->password = $value;
                }
            } else if ('end_at' == $key) {
                $user->$key = $value . ' 23:59:59';
            } else {
                $user->$key = $value;
            }
        }
        $user->last_update_at = now();
        $user->save();

        if ("" != $password->password) {
            $password->user_id = $user->id;
            $password->save();
        }

        return redirect(route('manage.list'));
    }

}
