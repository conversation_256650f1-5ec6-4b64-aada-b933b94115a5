<?php

namespace App\Http\Controllers\Manage;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserInputSheet as UserInputSheetResource;
use App\Models\MstInputSample;
use App\Models\User;
use App\Models\UserInputSheet;

class HistoryController extends Controller
{
    public function index(User $user)
    {
        $sample = MstInputSample::find(1);
        $histories = UserInputSheetResource::collection(UserInputSheet::where('user_id', $user->id)->get());
        return [
            'success' => true,
            'sample' => $sample->account_json,
            'histories' => $histories,
            'user' => $user,
        ];
    }
}
