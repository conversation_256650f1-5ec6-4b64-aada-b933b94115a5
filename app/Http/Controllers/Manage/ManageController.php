<?php

namespace App\Http\Controllers\Manage;

use App\Http\Controllers\Controller;
use App\Models\MstTheme;
use App\Models\User;
use App\Models\UserPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class ManageController extends Controller
{

    public function index(Request $request)
    {
        $allUuser = User::orderBy('id', 'desc')->get();
        $search_list = [];
        $users = [];
        foreach ($allUuser as $key => $user) {
            $search_list[] = [
                "index" => $user->id,
                "word" => "{$user->company_name} || {$user->user_name} || {$user->account_id}",
            ];

            $data = [
                'id' => $user->id,
                'info_id' => "info_".$user->id,
                'company_name' => $user->company_name,
                'user_name' => $user->user_name,
                'account_id' => $user->account_id,
                'show_link' => "$user->show_link",
                'show_pdf_company_name' => "$user->show_pdf_company_name",
                'max_input_sheets' => $user->max_input_sheets,
                'start_at' => $user->start_at->format('Y-m-d'),
                'end_at' => $user->end_at->format('Y-m-d'),
                'mst_theme_id' => $user->mst_theme_id,
                'last_login_date' => ($user->last_login_at) ? $user->last_login_at->format('Y-m-d') : "",
                'last_login_time' => ($user->last_login_at) ? $user->last_login_at->format('H:i:s') : "",
                'last_login_at' => ($user->last_login_at) ? $user->last_login_at->format('Y-m-d H:i:s') : "",
            ];
            $password = UserPassword::where('user_id', $user->id)->first();
            if ($password) {
                $decpass = '';
                try {
                    $decpass = Crypt::decryptString($password->password);
                } catch (DecryptException $e) {
                    \Log::warning('Invalid password decrypt '.$user->account_id);
                    $decpass = $password->password;
                }
                $data['password'] = $decpass;
            } else {
                $data['password'] = '********';
            }
            $users[] = $data;
        }

        $themes = MstTheme::all();
        $initdate['start_at'] = Carbon::today()->format('Y-m-d');
        $initdate['end_at'] = Carbon::tomorrow()->format('Y-m-d');

        return view('manage.list', compact('users', 'themes', 'initdate'));
    }

    public function entry(Request $request)
    {
        $validatedData = $request->validate([
            'company_name' => ['required', 'string', 'max:255'],
            'user_name'    => ['required', 'string', 'max:255'],
            'account_id'   => ['required', 'string', 'max:255'],
            'password'     => ['required', 'string', 'max:16'],
            'start_at'     => ['required', 'date'],
            'end_at'       => ['required', 'date', 'after:start_at'],
            'mst_theme_id' => ['required', 'integer', 'exists:mst_themes,id'],
            'show_link'    => ['required', 'integer', 'max:1'],
            'show_pdf_company_name'    => ['required', 'integer', 'max:1'],
            'max_input_sheets'         => ['required', 'integer'],
        ]);

        $user_id = $request->input('user_id');
        if ("" == $user_id) {
            // 新規登録
            $user = new User();
            $password = new UserPassword();
            $request->validate(
                ['account_id' => [Rule::unique('users', 'account_id')->whereNull('deleted_at')]]
            );
        } else {
            // 編集
            $request->validate(['user_id' => ['exists:users,id']]);
            $user = User::find($user_id);
            $password = UserPassword::where('user_id', $user_id)->first();
            if (!$password) {
                $password = new UserPassword();
            }

            // ID変更してる場合は重複チェック
            if ($user->account_id != $request->input('account_id')) {
                $request->validate(
                    ['account_id' => [Rule::unique('users', 'account_id')->whereNull('deleted_at')]]
                );
            }
        }
        foreach ($validatedData as $key => $value) {
            if ('password' == $key) {
                if ("" == $user_id || '********' != $value) {
                    $user->$key = Hash::make($value);
                    $password->password = Crypt::encryptString($value);
                }
            } else if ('end_at' == $key) {
                $user->$key = $value . ' 23:59:59';
            } else {
                $user->$key = $value;
            }
        }
        $user->save();

        if ("" != $password->password) {
            $password->user_id = $user->id;
            $password->save();
        }

        return redirect('/manage');
    }

}
