<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{

    /**
     * 自身のユーザーIDを除いたユーザーデータ取得処理
     *
     * @param $user_id
     * @return array
     */
    public function getUsersExcludingOwnId($user_id)
    {
        \Log::debug('user_id:'.$user_id);
        $userDatas = User::where('id', '<>', $user_id)->get();
        return ['success' => true, 'userDatas' => $userDatas];
    }

    /**
     * ユーザー取得処理
     *
     * @return array
     */
    public function getUsers()
    {
        return ['success' => true, 'userDatas' => User::all()];
    }
}
