<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TermAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = $request->user();
        $start = new Carbon($user->start_at);
        $end   = new Carbon($user->end_at);
        $now   = new Carbon();
        if ($start > $now || $now > $end) {
            Auth::logout();
            return redirect()->back()->withErrors(['expired' => true]);
        }
        $user->last_login_at = $now->format('Y-m-d H:i:s');
        $user->save();

        return $next($request);
    }
}
