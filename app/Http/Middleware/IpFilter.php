<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\IpUtils;

class IpFilter
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 何もしなくても$request->ip()でX_FORWARDED_FORのIP取れる
        // var_dump($request->ip());
        // $request->setTrustedProxies($request->ips(), Request::HEADER_X_FORWARDED_FOR);
        // var_dump($request->getTrustedProxies());
        // var_dump($request->isFromTrustedProxy());
        // var_dump($request->ip());

        $allowed = null;
        if ($request->is('manage/*')) {
            $allowed = config("allowipaddresses.manage");
        } else {
            $allowed = config("allowipaddresses.web");
        }

        if (!empty($allowed) && !IpUtils::checkIp($request->ip(), $allowed)) {
            abort(404);
        }

        return $next($request);
    }
}
