<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'company_name',
        'account_id',
        'start_at',
        'end_at',
        'mst_theme_id',
        'show_link',
        'last_login_at',
        'last_update_at',
        'show_pdf_company_name',
        'max_input_sheets'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $dates = ['start_at', 'end_at', 'last_login_at'];

    protected $with = ['theme'];

    /**
     * モデルの配列形態に追加するアクセサ
     *
     * @var array
     */
    protected $appends = ['listed_company_info_link'];

    public function theme()
    {
        return $this->belongsTo('App\Models\MstTheme', 'mst_theme_id');
    }

    public function inputSheets()
    {
        return $this->hasMany('App\Models\UserInputSheet');
    }

    /**
     * 上場情報リンク設定アクセサ
     *
     * @return void
     */
    public function getListedCompanyInfoLinkAttribute()
    {
        return config('listedCompanyInfoLinks.link');
    }
}
