<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserInputSheet extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'grouping_name', 'data_name', 'data_date',
        'account_json', 'constitution_json', 'filtered_account_json',
        'disable_chart', 'capitalize_debt_from_president'
    ];

    protected $casts = [
        'user_id' => 'integer',
    ];

    protected $dates = ['data_date'];

    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }
}
