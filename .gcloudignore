# This file specifies files that are *not* uploaded to Google Cloud Platform
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# file at that point).
#
# For more information, run:
#   $ gcloud topic gcloudignore
#
.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line
# below:
.git
.gitignore
.gitattributes

# PHP Composer dependencies:
/vendor/

/databese/
/storage/
/tests/
/node_modules/
/.vagrant/
/resources/js/
/resources/sass/
.eslintignore
.eslintrc.js
.styleci.yml
.editorconfig
after.sh
aliases
Homestead.yaml
package-lock.json
package.json
phpunit.xml
README.md
Vagrantfile
