#!/bin/bash
set -e

# initial setup
if [ ! -f ".env" ]; then
    echo "Creating .env file..."
    cp .env.example .env

    php artisan key:generate
fi

# run composer
if [ ! -d "vendor" ]; then
    echo "Installing Composer dependencies..."
    composer install --no-interaction --no-plugins --no-scripts
fi

# run npm
if [ ! -d "node_modules" ]; then
    echo "Installing npm dependencies..."
    npm install

    echo "Building assets..."
    npm run dev
fi

chmod -R 777 storage bootstrap/cache

# migrate
# php artisan migrate --force

exec "$@"
