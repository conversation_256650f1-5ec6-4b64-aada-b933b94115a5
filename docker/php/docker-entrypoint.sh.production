#!/bin/bash
set -e

# Function to start Cloud SQL Auth Proxy
start_sql_proxy() {
    if [ -n "${GOOGLE_CLOUD_SQL_CONNECTION_NAME}" ]; then
        echo "Starting Cloud SQL Auth Proxy..."

        # Create directory for Unix socket if needed
        mkdir -p /cloudsql

        # Start Cloud SQL Auth Proxy in background
        cloud-sql-proxy "${GOOGLE_CLOUD_SQL_CONNECTION_NAME}" &

        # Store PID for cleanup
        SQL_PROXY_PID=$!
        echo "Cloud SQL Auth Proxy started with PID: $SQL_PROXY_PID"

        # Wait for prpoxy to be ready
        echo "Waiting for Cloud SQL Auth Proxy to be ready..."
        for i in {1..30}; do
            if nc -z localhost 3306 2>/dev/null; then
                echo "Cloud SQL Auth Proxy is ready!"
                break
            fi
            if [ "${i}" -eq 30 ]; then
                echo "ERROR: Cloud SQL Auth Proxy failed to start within 30 seconds"
                exit 1
            fi
            sleep 1
        done
    else
        echo "Cloud SQL Auth Proxy not configured (missing GOOGLE_CLOUD_SQL_CONNECTION_NAME)"
    fi
}

# Function to cleanup on exit
cleanup() {
    if [ -n "$SQL_PROXY_PID" ]; then
        echo "Stopping Cloud SQL Auth Proxy (PID: $SQL_PROXY_PID)..."
        kill "${SQL_PROXY_PID}" 2>/dev/null || true
        wait "${SQL_PROXY_PID}" 2>/dev/null || true
    fi
}

# Set up signal handlers
trap cleanup EXIT TERM INT

# Start Cloud SQL Auth Proxy
start_sql_proxy

# Set proper permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# Execute the main command
exec "$@"
