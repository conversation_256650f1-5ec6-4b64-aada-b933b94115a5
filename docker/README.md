# TGM

## Initial Setup

```bash
# It is unnecessary, but it is recommended to create a new .env file before initial run
cp .env.example .env
# edit .env

docker compose build --no-cache
docker compose up

# From another terminal
docker cp docs/dumped.sql db:/tmp
docker compose exec db bash
mysql -u root -p tgm < /tmp/dumped.sql
```

## Run

```bash
docker compose up --build -d
```

## Notice

Source code in container is located in volume `app_code`. Run `docker volume rm *_app_code` if you want to refresh it.
