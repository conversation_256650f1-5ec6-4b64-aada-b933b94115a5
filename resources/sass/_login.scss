@import 'normalize';

* {
  box-sizing: border-box !important;
  font-family: 'Hiragino Kaku <PERSON> Pro', 'メイリオ', 'Helvetica Neue', 'Arial', sans-serif !important;
}

.wrapper {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .inner {
    text-align: center;
    .formBox {
      background-color: #e5e9f2;
      border: solid 0px #397f00;
      margin: 25px auto;
      padding: 30px 20px;
      width: 500px;
      -webkit-border-radius: 10px;
      -moz-border-radius: 10px;
      border-radius: 10px;
      .loginBtnFromItem {
        margin-bottom: 0;
        .el-form-item__content {
          margin-left: 0 !important;
          margin: 0 auto;
        }
      }
    }
  }
}

.manageStyle {
  input[type="text"], input[type="password"] {
    border-radius: 5px;
    &:focus {
      border: 1px solid #397f00;
      outline: 0;
    }
  }

  .el-button {
    &:active {
      background: #397f00;
      border-color: #397f00;
      outline: 0;
    }
    &:focus, &:hover {
      color: #397f00;
      background: #9be95c;
      border-color: #9be95c;
    }
  }

  .el-button--primary {
    color: #fff;
    background-color: #397f00;
    border-color: #397f00;
    &:focus, &:hover {
      background: #9be95c;
      border-color: #9be95c;
    }
  }

  .el-button--primary.is-plain {
    color: #397f00;
    background: #cefaab;
    border-color: #9be95c;
  }

  .el-button--primary.is-active, .el-button--primary:active {
    background: #397f00;
    border-color: #397f00;
    color: #FFF;
  }
}

.userStyle {
  input[type="text"], input[type="password"] {
    border-radius: 5px;
    &:focus {
      border: 1px solid #ef9c5d;
      outline: 0;
    }
  }

  .el-button {
    &:active {
      background: #ef9c5d;
      border-color: #ef9c5d;
      outline: 0;
    }
    &:focus, &:hover {
      color: #ef9c5d;
      background: #f5dab1;
      border-color: #f5dab1;
    }
  }

  .el-button--primary {
    color: #fff;
    background-color: #ef9c5d;
    border-color: #ef9c5d;
    &:focus, &:hover {
      background: #f5dab1;
      border-color: #f5dab1;
    }
  }

  .el-button--primary.is-plain {
    color: #ef9c5d;
    background: #fdf6ec;
    border-color: #f5dab1;
  }

  .el-button--primary.is-active, .el-button--primary:active {
    background: #ee8434;
    border-color: #ee8434;
    color: #FFF;
  }
}
