//element-uiのカスタマイズ
@import 'variables';

// 確認メッセージボックスのボタン間隔
.el-message-box__btns button:nth-child(2) {
  margin-left: 30px;
}

input[type="text"] {
  border-radius: 5px;
  &:focus {
    border: 1px solid $el-color-primary;
    outline: 0;
  }
}

.el-button {
  &:active {
    background: $el-color-primary;
    border-color: $el-color-primary;
    outline: 0;
  }
  &:focus, &:hover {
    color: $el-color-primary;
    background: #9be95c;
    border-color: #9be95c;
  }
}

.el-button--primary {
  color: #fff;
  background-color: $el-color-primary;
  border-color: $el-color-primary;
  &:focus, &:hover {
    background: #9be95c;
    border-color: #9be95c;
  }
}

.el-button--primary.is-plain {
  color: $el-color-primary;
  background: #cefaab;
  border-color: #9be95c;
}

.el-button--primary.is-active, .el-button--primary:active {
  background: $el-color-primary;
  border-color: $el-color-primary;
  color: #FFF;
}

.el-menu--horizontal>.el-menu-item.is-active {
  border-bottom: 2px solid $el-color-primary;
}

.el-button--info { color: #909399; background-color: #ffffff; border-color: #ffffff;}

.el-link.el-link--primary {
  text-decoration: none;
  color: #606266;
  &:hover {
    color: #606266;
  }
}

.el-button--info {
  color: #909399;
  background-color: #ffffff;
  border-color: #ffffff;
}

.el-button--mini, .el-button--small {
  font-size: 18px;
  border-radius: 3px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: $el-color-primary;
  background: $el-color-primary;
}

.el-radio__input.is-checked+.el-radio__label {
  color: $el-color-primary;
}

.el-radio__inner:hover {
  border-color: $el-color-primary;
}
