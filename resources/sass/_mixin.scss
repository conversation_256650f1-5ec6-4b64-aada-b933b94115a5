@mixin content-width($margin-top:0, $margin-bottom:0) {
  min-width:570px;
  max-width:830px;
  width:70%;
  margin: $margin-top auto $margin-bottom;
}

@mixin button-area-pdf {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-bottom: 25px;
  background-color: $button-area-color;
  padding: 10px 30px;
}

@mixin menu-item {
  font-size: 14px;
  color: #303133;
  padding: 0 20px;
  list-style: none;
  position: relative;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  white-space: nowrap;
  float: left;
  height: 60px;
  line-height: 60px;
  margin: 0;
  border-bottom: 2px solid transparent;
  outline: none;
}
