@import 'normalize';
@import 'variables';

* {
  box-sizing: border-box !important;
  font-family: 'Hiragino Kaku Gothic Pro', 'メイリオ', 'Helvetica Neue', 'Arial', sans-serif !important;
}

// グラフの色指定
.chart {
  svg {
    text {
      fill: $text-color-base;
    }
    .color3 text {
      fill: $text-color-reverse;
    }
    rect {
      stroke: $table-border-color;
    }
  }
}
.pdfchart {
  svg {
    .color3 text {
      fill: $text-color-reverse;
    }
  }
}

// ライブラリのcssの読み方変えたら治ったのでいらない
// // ライブラリのcssが適用されないので、コピーしておく
// .el-popover {
//   position: absolute;
//   background: #fff;
//   min-width: 150px;
//   border-radius: 4px;
//   border: 1px solid #ebeef5;
//   padding: 12px;
//   z-index: 2000;
//   color: #606266;
//   line-height: 1.4;
//   text-align: justify;
//   font-size: 14px;
//   box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
//   word-break: break-all;
// }
