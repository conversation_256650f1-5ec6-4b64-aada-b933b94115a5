//element-uiのカスタマイズ
@import 'variables';

.is-circle {
  &.el-button--mini {
    font-size: 18px;
    border-color: #ffffff;
  }
}
.el-button:not(.el-button--text) {
  &:active {
    background: $el-color-primary;
    border-color: $el-color-primary;
    outline: 0;
  }
  &:focus, &:hover {
    color: $el-color-primary;
    background: #f5dab1;
    border-color: #f5dab1;
  }
}

// 確認メッセージボックスのボタン間隔
.el-message-box__btns button:nth-child(2) {
  margin-left: 30px;
}

/*
.el-button--mini, .el-button--small { font-size: 18px; border-radius: 3px;}

input[type="text"]:focus { border: 1px solid #ef9c5d;}

// color
.el-button--primary {
  color: #fff;
  background-color: #ef9c5d;
  border-color: #ef9c5d;
}

.el-button--primary.is-plain {
  color: #ef9c5d;
  background: #fdf6ec;
  border-color: #f5dab1;
}

.el-button--primary.is-active, .el-button--primary:active {
  background: #ee8434;
  border-color: #ee8434;
  color: #FFF;
}

.el-button--primary:focus, .el-button--primary:hover {
  background: #f5dab1;
  border-color: #f5dab1;
  color: #FFF;
}


.el-button--primary.is-disabled, .el-button--primary.is-disabled:active, .el-button--primary.is-disabled:focus, .el-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #f6ceb0;
  border-color: #f6ceb0;
}


.el-button:active {
  background: #ef9c5d;
  border-color: #ef9c5d;
  outline: 0;
}
.el-button:focus, .el-button:hover {
  color: #ef9c5d;
  background: #f5dab1;
  border-color: #f5dab1;
}

.el-menu--horizontal>.el-menu-item.is-active {
  border-bottom: 2px solid #ef9c5d;
  color: #303133;
}

.el-button--info { color: #909399; background-color: #ffffff; border-color: #ffffff;}
*/
