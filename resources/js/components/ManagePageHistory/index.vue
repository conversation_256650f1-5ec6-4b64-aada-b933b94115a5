<template>
  <TemplateHisotry :is-manage="true">
    <template v-slot:table-button-area="scope">
      <Button
        size="mini"
        @click="show(scope.row.id)"
      >
        表示
      </Button>
    </template>
  </TemplateHisotry>
</template>

<script>
import TemplateHisotry from '../share/TemplateHistory';
import { Button } from 'element-ui';

export default {
  components: { TemplateHisotry, Button },
  methods: {
    show(id) {
      this.$store.dispatch('showManage', { id })
    }
  }
}
</script>
