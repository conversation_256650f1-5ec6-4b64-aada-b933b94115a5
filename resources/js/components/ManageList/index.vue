<template>
  <div style="width: 75%; margin: 0 auto;">
    <h3>ユーザー一覧</h3>

    <div style="padding: 10px 30px; margin: 10px; background: #e5e9f2;">
      <Input
        v-model="search"
        class="searchInput"
        style="width: 30%;"
        placeholder="検索"
      />
      <Button
        icon="el-icon-edit"
        type="primary"
        style="float: right;"
        @click="createEvent()"
      >
        新規登録
      </Button>
    </div>

    <Table
      :data="users.filter(data => !search
        || data.company_name.toLowerCase().includes(search.toLowerCase())
        || data.user_name.toLowerCase().includes(search.toLowerCase())
        || data.account_id.toLowerCase().includes(search.toLowerCase())
      )"
      height="600"
      style="width: 100%;"
      :row-class-name="tableRowClassName"
      @cell-click="handleClick"
    >
      <TableColumn
        prop="company_name"
        label="企業名"
        header-align="center"
        sortable
      />
      <TableColumn
        prop="user_name"
        label="ユーザー名"
        header-align="center"
        sortable
      />
      <TableColumn
        prop="account_id"
        label="アカウントID"
        header-align="center"
        sortable
      />
      <TableColumn
        prop="show_link"
        label="上場情報リンク"
        align="center"
        header-align="center"
      >
        <template slot-scope="scope">
          <div>{{ ("1" === scope.row.show_link) ? "ON" : "OFF" }}</div>
        </template>
      </TableColumn>
      <TableColumn
        prop="start_at"
        label="開始日"
        align="center"
        header-align="center"
        sortable
      >
        <template slot-scope="scope">
          <div>{{ scope.row.start_at }}</div>
        </template>
      </TableColumn>
      <TableColumn
        prop="end_at"
        label="期限日"
        align="center"
        header-align="center"
        sortable
      >
        <template slot-scope="scope">
          <div>{{ scope.row.end_at }}</div>
        </template>
      </TableColumn>
      <TableColumn
        prop="last_login_at"
        label="最終ログイン日"
        align="center"
        header-align="center"
        sortable
      >
        <template slot-scope="scope">
          <div style="display:none;">
            {{ scope.row.last_login_at }}
          </div>
          <div>{{ scope.row.last_login_date }} <br> {{ scope.row.last_login_time }}</div>
        </template>
      </TableColumn>
      <TableColumn
        prop="clipboard"
        width="50"
      >
        <template slot-scope="scope">
          <Popover
            title="コピーする情報"
            trigger="hover"
            placement="left"
          >
            <p>企業名：{{ scope.row.company_name }}</p>
            <p>名前：{{ scope.row.user_name }}</p>
            <p>アカウントID：{{ scope.row.account_id }}</p>
            <p>パスワード：{{ scope.row.password }}</p>
            <p>利用開始日：{{ scope.row.start_at }}</p>
            <p>利用期限日：{{ scope.row.end_at }}</p>
            <div slot="reference">
              <Button
                type="info"
                size="mini"
                icon="el-icon-copy-document"
                circle
              />
            </div>
          </Popover>
<textarea
  :id="scope.row.info_id"
  style="display:none;"
>
企業名：{{ scope.row.company_name }}
名前：{{ scope.row.user_name }}
アカウントID：{{ scope.row.account_id }}
パスワード：{{ scope.row.password }}
利用開始日：{{ scope.row.start_at }}
利用期限日：{{ scope.row.end_at }}
</textarea>
        </template>
      </TableColumn>
    </Table>
  </div>
</template>

<script>
import { Input, Button, Popover, Table, TableColumn, Message } from 'element-ui'

export default {
  components: { Input, Button, Popover, Table, TableColumn },
  props: {
    users: {type: Array, required: true },
  },
  data() {
    return {
      search: '',
    };
  },
  methods: {
    tableRowClassName({ row }) {
      var now = new Date();
      // Mac safari対応 日付形式をスラッシュに変換
      var end = new Date(row.end_at.replace(/-/g, '/') + ' 23:59:59');
      var start = new Date(row.start_at.replace(/-/g, '/') + ' 00:00:00');
      if (end < now || start > now) {
        return 'expired-row';
      }
      return '';
    },
    handleClick(row, column) {
      if ('clipboard' == column.property) {
        this.handleCopy(row.info_id);
      } else {
        this.$emit('select', 'edit', row);
      }
    },
    handleCopy(id) {
      if (navigator.clipboard) {
        const copyText = this.$el.querySelector('#'+id).textContent;
        navigator.clipboard.writeText(copyText)
          .then(() => {
            Message.success('ユーザー情報をクリップボードにコピーしました');
          })
          .catch(() => {
            Message.error('コピー失敗');
          })
      } else {
        var copyElem = document.getElementById(id);
        copyElem.style.display = "";
        copyElem.select();
        document.execCommand("copy");
        copyElem.style.display = "none";
        Message.success('ユーザー情報をクリップボードにコピーしました');
      }
    },
    jumpUrl(url) {
      location.href = url;
    },

    createEvent() {
      this.$emit('select', 'create', null);
    },
  }
};
</script>

<style lang="scss">
@import 'resources/sass/manage/variables';

.el-table {
  .expired-row {
    background: lightgray;
  }
  .ascending .sort-caret.ascending {
    border-bottom-color: $el-color-primary;
  }
  .descending .sort-caret.descending {
    border-top-color: $el-color-primary;
  }
}

.searchInput {
  input:focus {
      border: 1px solid $el-color-primary;
      outline: 0;
  }
}
</style>
