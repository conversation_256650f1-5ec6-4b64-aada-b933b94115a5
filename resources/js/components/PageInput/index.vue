<template>
  <div class="PageInput">
    <TemplateSheet ref="template">
      <template v-slot:form>
        <Form
          ref="form"
          method="post"
          action="/pdf/constitution"
          :model="sheetData"
          :rules="rules"
          label-width="80px"
          class="sheet-data-from"
        >
          <input
            type="hidden"
            name="_token"
            :value="csrfToken"
          >
          <FormItem
            label="企業名"
            prop="groupingName"
          >
            <Input
              v-model="groupingName"
              name="groupingName"
              maxlength="200"
              @change="setDirty"
            />
          </FormItem>
          <FormItem
            label="対象月"
            required
          >
            <FormItem prop="dataDate">
              <DatePicker
                v-model="dataDate"
                name="dataDate"
                type="date"
                value-format="yyyy-MM-dd"
                @change="setDirty"
              />
            </FormItem>
          </FormItem>
          <FormItem label="タイトル">
            <Input
              v-model="dataName"
              name="dataName"
              maxlength="10"
              show-word-limit
              @change="setDirty"
            />
          </FormItem>
          <FormItem class="checkbox">
            <Checkbox v-model="capitalizeDebtFromPresident">
              グラフの役員借入金を資本とする
            </Checkbox>
          </FormItem>
          <!-- <div style="width: 100%; margin: 25px auto 25px; background-color: #ffffff; border: solid 2px #F56C6C; color: #F56C6C; border-radius: 4px; padding: 8px 16px; text-align: center;}">
            <span style="font-size:14px;">1.95%以下の構成比はグラフに反映されません</span>
          </div> -->
        </Form>
      </template>
      <template v-slot:button-area-left>
        <Popconfirm
          title="数値をクリアしてよろしいですか？"
          confirm-button-text="はい"
          cancel-button-text="いいえ"
          :hide-icon="true"
          @onConfirm="clearAmounts"
        >
          <Button slot="reference">
            数値クリア
          </Button>
        </Popconfirm>
      </template>
      <template v-slot:button-area-right>
        <Button
          type="primary"
          :disabled="!sheetCanAdd"
          @click="handleStore"
        >
          新規登録
        </Button>
        <Button
          v-if="sheetData.id"
          type="primary"
          @click="handleUpdate"
        >
          上書き登録
        </Button>
      </template>
    </TemplateSheet>
  </div>
</template>

<script>
import TemplateSheet from '../share/TemplateSheet';
import { Input, DatePicker, Checkbox, Button, Form, FormItem, Popconfirm, Loading, MessageBox } from 'element-ui';
import { mapState, mapGetters, mapActions, mapMutations } from "vuex";

export default {
  components: { TemplateSheet, Input, DatePicker, Checkbox, Button, Form, FormItem, Popconfirm },
  data() {
    // const checkGroupingNum = (rule, value, callback) => {
    //   if (!this.$store.getters.groupCanAdd(value)) {
    //     callback(new Error('登録できる企業名は30個までです。'));
    //   } else {
    //     callback();
    //   }
    // };
    return {
      rules: {
        // groupingName: [
        //   { validator: checkGroupingNum, trigger: 'blur' },
        // ],
        dataDate: [
          { required: true, message: '対象月を入力してください', trigger: 'change' },
        ],
      },
    }
  },
  computed: {
    ...mapState(["csrfToken", "histories"]),
    ...mapState({ sheetData: state => state.current}),
    ...mapGetters(["sheetCanAdd"]),
    ...mapGetters('current', ["filteredAccount", "constitution"]),
    groupingName: {
      get() {
        return this.$store.state.current.groupingName
      },
      set(value) {
        this.$store.commit('current/updateProp', { propName: 'groupingName', value})
      }
    },
    dataDate: {
      get() {
        return this.$store.state.current.dataDate
      },
      set(value) {
        this.$store.commit('current/updateProp', { propName: 'dataDate', value})
      }
    },
    dataName: {
      get() {
        return this.$store.state.current.dataName
      },
      set(value) {
        this.$store.commit('current/updateProp', { propName: 'dataName', value})
      }
    },
    capitalizeDebtFromPresident: {
      get() {
        return this.$store.state.current.capitalizeDebtFromPresident
      },
      set(value) {
        this.$store.commit('current/updateProp', { propName: 'capitalizeDebtFromPresident', value})
      }
    },
  },
  methods: {
    ...mapActions(["editInputSheet"]),
    ...mapMutations('current', ["clearAmounts", "setDirty"]),
    handleStore() {
      const e = this.$refs.template.$refs.sheet.$refs.row.find(e => !e.valid)
      if (e) {
        e.$refs.account.$refs.input.focus()
        return
      }
      this.$refs['form'].validate((valid) => {
        if (!valid) return
        MessageBox.confirm('キャッシュ体質図を新規登録してもよろしいですか？', '注意', {
          type: 'warning',
          center: true
        }).then(() => {
          let loadingInstance = Loading.service({ fullscreen: true, text: 'Loading', lock: true });
          this.$store.dispatch('storeInputSheet')
          .then(() => {
            loadingInstance.close();
          })
          .catch(() =>{
            loadingInstance.close();
            MessageBox.alert('データの保存処理でエラーが発生しました。', 'エラー', {
              type: 'error',
              center: true
            });
          })
        })
      });
    },
    handleUpdate() {
      if (!this.$refs.template.$refs.sheet.$refs.row.every(e => e.valid)) {
        return
      }
      this.$refs['form'].validate((valid) => {
        if (!valid) return
        MessageBox.confirm('キャッシュ体質図を上書き登録してもよろしいですか？', '注意', {
          type: 'warning',
          center: true
        }).then(() => {
          let loadingInstance = Loading.service({ fullscreen: true, text: 'Loading', lock: true });
          this.$store.dispatch('updateInputSheet')
          .then(() => {
            loadingInstance.close();
          })
          .catch(() =>{
            loadingInstance.close();
            MessageBox.alert('データの保存処理でエラーが発生しました。', 'エラー', {
              type: 'error',
              center: true
            });
          })
        })
      });
    },
    onShowChart() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.chartVisible = true;
        }
      });
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';
@import 'resources/sass/_mixin.scss';

.PageInput {
  .sheet-data-from {
    @include content-width(25px, 25px);
  }
  .ButtonArea {
    @include content-width(10px, 22px);
    background-color: $button-area-color;
    >span {
      flex-grow: 1;
    }
  }
  .Sheet {
    @include content-width
  }
  .checkbox .el-form-item__content {
    line-height: 1.15;
  }
}
</style>
