<template>
  <Tabs
    class="HistoryTabTable"
    type="card"
    :value="selectedGroupIndex"
    @tab-click="$emit('tab-click', $event.label)"
  >
    <TabPane
      v-for="[name, hisotry] of groupingData"
      :key="name"
      :label="name"
    >
      <div
        class="date-tooltip el-tooltip__popper is-dark ascending"
        x-placement="right"
      >
        昇順
        <div
          x-arrow
          class="popper__arrow"
        />
      </div>
      <div
        class="date-tooltip el-tooltip__popper is-dark descending"
        x-placement="right"
      >
        降順
        <div
          x-arrow
          class="popper__arrow"
        />
      </div>
      <Table
        :data="hisotry"
        :default-sort="{prop: 'dataDate', order: 'descending'}"
        :height="tableHeignt"
        @selection-change="$emit('selection-change', $event)"
      >
        <TableColumn
          type="selection"
          :selectable="selectable"
          width="43"
          align="center"
        />
        <TableColumn
          label="データ名"
          prop="dataName"
          width="343"
        />
        <TableColumn
          label="対象月"
          prop="dataDate"
          width="124"
          sortable
          align="center"
        />
        <TableColumn
          label="最終編集日時"
          prop="updatedAt"
          width="142"
          align="center"
          class-name="datetime"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.updatedDate }} <br> {{ scope.row.updatedTime }}</div>
          </template>
        </TableColumn>
        <TableColumn width="168">
          <template v-slot="scope">
            <slot :row="scope.row" />
          </template>
        </TableColumn>
      </Table>
    </TabPane>
  </Tabs>
</template>

<script>
import { Tabs, TabPane, Table, TableColumn } from 'element-ui';
import { mapState, mapGetters } from "vuex";

export default {
  components: { Tabs, TabPane, Table, TableColumn },
  data() {
    return {
      innerHeight: window.innerHeight,
      top: 150,
    }
  },
  computed: {
    ...mapState("ui", ["selectedGroup"]),
    ...mapGetters(["groupingData"]),
    ...mapGetters("ui", ["selectedGroupIndex"]),
    tableHeignt() {
      return this.innerHeight - this.top - 110;
    },
  },
  created() {
    window.addEventListener('resize', this.handleResize, false);
  },
  mounted() {
    // 対象月ソートのツールチップ表示
    this.$nextTick(function() {
      {
        let t = document.getElementsByClassName('date-tooltip ascending');
        let obj = document.getElementsByClassName('sort-caret ascending');
        let length = obj.length;
        for(let i = 0; i < length; i++) {
        obj[i].onmouseover = () => { t[i].style.display = 'block'; }
        obj[i].onmouseout = () => { t[i].style.display = 'none'; }
        }
      }
      {
        let t = document.getElementsByClassName('date-tooltip descending');
        let obj = document.getElementsByClassName('sort-caret descending');
        let length = obj.length;
        for(let i = 0; i < length; i++) {
        obj[i].onmouseover = () => { t[i].style.display = 'block'; }
        obj[i].onmouseout = () => { t[i].style.display = 'none'; }
        }
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize, false);
  },
  methods: {
    handleResize() {
      this.innerHeight = window.innerHeight;
    },
    selectable(row) {
      return !row.disableChart
    },
  }
}
</script>

<style lang="scss">
.HistoryTabTable {
  .el-table {
    font-size: unset;
  }
  .datetime .cell {
    word-break: break-word;
  }
  .date-tooltip {
    position: absolute;
    left: 470px;
    display: none;
    &:nth-child(1) {
      top: 0px;
    }
    &:nth-child(2) {
      top: 20px;
    }
    div {
      top: 11px;
    }
  }
}
</style>
