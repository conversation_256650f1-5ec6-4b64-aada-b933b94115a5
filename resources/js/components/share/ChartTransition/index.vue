<template>
  <Tabs
    class="ChartTransition"
    type="card"
  >
    <TabPane label="推移A">
      <div class="header">
        <span>{{ leftTitle }}</span>
        <span>キャッシュ体質推移A</span>
        <span>{{ rightTitle }}</span>
      </div>
      <div class="unitDisp">
        <span>(単位：{{ unit.label }})</span>
      </div>
      <div
        id="ac"
        class="chart"
      />
      <div
        id="acpdf"
        ref="chartac"
        class="pdf"
      />
      <div class="CompanyName">
        {{ user.show_pdf_company_name ? user.company_name : '' }}
      </div>
    </TabPane>
    <TabPane label="推移A'">
      <div class="header">
        <span>{{ leftTitle }}</span>
        <span>キャッシュ体質推移A'</span>
        <span>{{ rightTitle }}</span>
      </div>
      <div class="unitDisp">
        <span>(単位：{{ unit.label }})</span>
      </div>
      <div
        id="ad"
        class="chart"
      />
      <div
        id="adpdf"
        ref="chartad"
        class="pdf"
      />
      <div class="CompanyName">
        {{ user.show_pdf_company_name ? user.company_name : '' }}
      </div>
    </TabPane>
    <TabPane label="推移B">
      <div class="header">
        <span>{{ leftTitle }}</span>
        <span>キャッシュ体質推移B</span>
        <span>{{ rightTitle }}</span>
      </div>
      <div class="unitDisp">
        <span>(単位：{{ unit.label }})</span>
      </div>
      <div
        id="bc"
        class="chart"
      />
      <div
        id="bcpdf"
        ref="chartbc"
        class="pdf"
      />
      <div class="CompanyName">
        {{ user.show_pdf_company_name ? user.company_name : '' }}
      </div>
    </TabPane>
    <TabPane label="推移B'">
      <div class="header">
        <span>{{ leftTitle }}</span>
        <span>キャッシュ体質推移B'</span>
        <span>{{ rightTitle }}</span>
      </div>
      <div class="unitDisp">
        <span>(単位：{{ unit.label }})</span>
      </div>
      <div
        id="bd"
        class="chart"
      />
      <div
        id="bdpdf"
        ref="chartbd"
        class="pdf"
      />
      <div class="CompanyName">
        {{ user.show_pdf_company_name ? user.company_name : '' }}
      </div>
    </TabPane>
  </Tabs>
</template>

<script>
import * as charts from "../../../charts";
import { Tabs, TabPane } from 'element-ui';
import { mapState } from "vuex";

export default {
  components: { Tabs, TabPane },
  props: {
    chartVisible: { type: Boolean, required: true },
    transition: { type: Array, default: function() {
      return []
    } },
    unit: { type: Object, required: true },
  },
  computed: {
    ...mapState(["user"]),
    leftTitle() {
      return this.transition[0] && this.transition[0].groupingName
    },
    rightTitle() {
      return ""
    },
  },
  watch: {
    chartVisible: function(val) {
      if (val) {
        this.drawChart()
      }
    },
    unit: function() {
      this.drawChart()
    }
  },
  mounted() {
    this.drawChart()
  },
  methods: {
    getSvg() {
      return JSON.stringify({
        'ac': this.$refs['chartac'].innerHTML,
        'ad': this.$refs['chartad'].innerHTML,
        'bc': this.$refs['chartbc'].innerHTML,
        'bd': this.$refs['chartbd'].innerHTML,
      });
    },
    drawChart() {
      charts.transition(
        this.transition,
        this.user,
        this.onClick,
        this.unit.value
      );
    },
    onClick(data) {
      this.$emit('click', data.data.id);
    },
  }
};
</script>

<style lang="scss">
.ChartTransition {
  margin: 0;
  .el-tab-pane .chart {
    overflow-x: scroll;
  }
  .el-tab-pane .pdf {
    display: none;
  }
  .header {
    display: flex;
    width: 720px;
    justify-content: center;
    align-items: flex-start;
    margin-top: 0.6em;
    font-size: 1.2em;
    span {
      flex-basis: 210px;
      &:nth-child(2n+1) {
        flex-grow: 1;
      }
      &:nth-child(2) {
        text-align: center;
      }
      &:nth-child(3) {
        text-align: right;
      }
    }
  }
  .unitDisp {
    width: 720px;
    margin-bottom: 0.4em;
    font-size: 0.9em;
    text-align: right;
  }
  .CompanyName {
    width: 720px;
    margin-top: 0.5em;
    font-size: 1.4em;
    text-align: right;
  }
}
</style>>
