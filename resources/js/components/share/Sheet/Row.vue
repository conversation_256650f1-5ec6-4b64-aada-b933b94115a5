<template>
  <tr class="Sheet-Row">
    <th
      v-if="fixed || index===0"
      :rowspan="fixed ? 1 : rowspan"
      :colspan="fixed ? 2 : 1"
    >
      {{ fixed ? account.name : middle.name }}
    </th>
    <td v-if="editable && !fixed && !src && !dest">
      <Form
        ref="rowForm"
        :model="form"
        :rules="rules"
      >
        <FormItem prop="account">
          <InputAccount
            ref="account"
            :value="account.name"
            @change="change"
            @enter="enter"
            @input="input"
            @keydown="keydownAccount($event)"
          />
          <input
            type="text"
            name="dummy"
            style="display:none"
          >
        </FormItem>
      </Form>
    </td>
    <td
      v-else-if="!fixed"
      class="static"
    >
      {{ account.name }}
    </td>
    <td>
      <InputAmount
        v-if="editable && !dest"
        ref="amount"
        :value="account.value"
        @change="updateAmount({ id: account.id, value: $event })"
        @keydown="keydownAmount($event)"
      />
      <span v-else>{{ format(account.value) }}</span>
    </td>
    <td>{{ parcent }}</td>
    <td v-if="editable">
      <Tooltip
        content="削除"
        placement="left"
      >
        <Button
          v-if="!fixed && !src && !dest"
          icon="el-icon-delete"
          circle
          size="mini"
          @click="deleteRow({ id: account.id })"
        />
      </Tooltip>
    </td>
  </tr>
</template>

<script>
import InputAmount from '../../parts/InputAmount';
import InputAccount from '../../parts/InputAccount';
import { Button, Form, FormItem, Tooltip } from 'element-ui';
import { mapMutations } from 'vuex'

const formatter = new Intl.NumberFormat('ja-JP', {style: 'percent', minimumFractionDigits: 1});
const amountFormatter = new Intl.NumberFormat('ja-JP');

export default {
  components: { InputAmount, InputAccount, Button, Form, FormItem, Tooltip },
  props: {
    editable: { type: Boolean, default: true },
    middle: { type: Object, required: true },
    index: { type: Number, required: true },
    account: { type: Object, required: true },
    total: { type: Number, required: true },
    hasNewRow: { type: Boolean, default: true },
  },
  data() {
    var checkUnique = (rule, value, callback) => {
      if (!this.middle.children.every(e => e.name !== value || e.id === this.account.id, this)) {
        callback(new Error('同じ科目があります。'));
      } else {
        callback();
      }
    };
    return {
      form: {
        account: this.account.name,
      },
      rules: {
        account: [
          { validator: checkUnique, trigger: 'change'}
        ]
      },
      valid: true,
    }
  },
  computed: {
    fixed() {
      return this.account.type==='fixed'
    },
    src() {
      return this.account.type==='src'
    },
    dest() {
      return this.account.type==='dest'
    },
    rowspan() {
      return this.hasNewRow ? this.middle.children.length + 1 : this.middle.children.length;
    },
    parcent() {
      return formatter.format(this.total === 0 ? 0: this.account.value / this.total);
    }
  },
  methods: {
    ...mapMutations('current', ["deleteRow", "updateAmount", "updateAcount"]),
    input(value) {
      this.form.account = value
      this.$refs['rowForm'].clearValidate();
    },
    update(change = false) {
      if (!this.$refs['rowForm']) return
      this.$refs['rowForm'].validate((valid) => {
        this.valid = valid
        if (valid) {
          if (change) {
            this.$emit('change', this)
          }
        }
      })
    },
    change(e) {
      // 科目名の入れ替えができるようにする
      // state.current.accountDataの科目名が重複しないようにしていたが、
      // バリデーションエラー時には保存できないようにしたので、変更後は即時でstate.current.accountDataへ反映する
      this.updateAcount({ id: this.account.id, value: e, parentName: this.middle.name })
      this.update(true)
      // その他で行が入れ替わるとフォーカスが行方不明なるので強制的に金額にフォーカスする
      // その時、遅延してenterイベントが起きてしまうので、InputAmountのイベントはkeydownに変更した
      if (e === 'その他') {
        this.enter()
      }
    },
    enter() {
      this.$refs['amount'].click();
    },
    keydownAccount(key) {
      if (key === "ArrowRight") {
        this.$refs['amount'].click();
      } else {
        this.$emit('moveAccount', { key, target: this })
      }
    },
    keydownAmount(key) {
      if (key === "ArrowLeft") {
        this.$refs['account'].$refs['input'].focus();
      } else {
        this.$emit('moveAmount', { key, target: this })
      }
    },
    format(value) {
      return amountFormatter.format(value);
    }
  }
}
</script>

<style lang="scss">
.Sheet-Row {
  td.static {
    padding-left: 9px;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__content {
    line-height: inherit;
    position: relative;
    font-size: inherit;
  }
  .el-form-item__error {
    position: relative;
  }
}
</style>
