<template>
  <tr class="Sheet-NewRow">
    <th v-if="header">
      {{ header }}
    </th>
    <td>
      <Form
        ref="newFrom"
        :model="form"
        :rules="rules"
      >
        <FormItem prop="account">
          <InputAccount
            ref="account"
            v-model="form.account"
            @change="change"
            @enter="enter"
            @keydown="keydownAccount($event)"
          />
          <input
            type="text"
            name="dummy"
            style="display:none"
          >
        </FormItem>
      </Form>
    </td>
    <td>
      <InputAmount
        ref="amount"
        :value="newValue"
        @change="changeAmount"
        @keydown="keydownAmount($event)"
      />
    </td>
    <td>0.0%</td>
    <td>&nbsp;</td>
  </tr>
</template>

<script>
import InputAmount from '../../parts/InputAmount';
import InputAccount from '../../parts/InputAccount';
import { Form, FormItem } from 'element-ui';
import { mapMutations } from 'vuex'

export default {
  components: { InputAmount, InputAccount, Form, FormItem },
  props: {
    header: { type: [Boolean, String], required: true },
    middle: { type: Object, required: true },
  },
  data() {
    var checkUnique = (rule, value, callback) => {
      if (!this.middle.children.every(e => e.name !== value)) {
        callback(new Error('同じ科目があります。'));
      } else {
        callback();
      }
    };
    return {
      form: {
        account: ""
      },
      newValue: 0,
      rules: {
        account: [
          { validator: checkUnique, trigger: 'change'}
        ]
      },
      valid: true,
    }
  },
  methods: {
    ...mapMutations('current', ["addRow"]),
    changeAmount(value) {
      this.newValue = value;
      this.change();
    },
    update(change = false) {
      this.$refs['newFrom'].validate((valid) => {
        this.valid = valid
        if (valid) {
          if (this.form.account && this.newValue) {
            this.addRow({ parentName: this.middle.name, name: this.form.account, value: this.newValue })
            this.form.account = "";
            this.$refs.account.setValue("");
            this.newValue = 0;
            this.$refs.amount.setValue(0);
          }
          if (change) {
            this.$emit('change', this)
          }
        }
      });
    },
    change() {
      this.update(true)
    },
    enter() {
      this.$refs['amount'].click();
    },
    keydownAccount(key) {
      if (key === "ArrowRight") {
        this.$refs['amount'].click();
      } else {
        this.$emit('moveAccount', { key, target: this })
      }
    },
    keydownAmount(key) {
      if (key === "ArrowLeft") {
        this.$refs['account'].$refs['input'].focus();
      } else {
        this.$emit('moveAmount', { key, target: this })
      }
    },
  }
}
</script>

<style lang="scss">
.Sheet-NewRow {
  td:nth-child(-n+2) {
    vertical-align: top;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__content {
    line-height: inherit;
    position: relative;
    font-size: inherit;
  }
  .el-form-item__error {
    position: relative;
  }
}
</style>
