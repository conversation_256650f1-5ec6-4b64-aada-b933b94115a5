<template>
  <div class="Sheet">
    <table>
      <colgroup>
        <col>
        <col>
        <col>
        <col>
        <col v-if="editable">
      </colgroup>
      <thead>
        <tr>
          <th>&nbsp;</th>
          <th>科目</th>
          <th>金額</th>
          <th>構成比</th>
          <th v-if="editable">
            &nbsp;
          </th>
        </tr>
      </thead>
      <tbody>
        <template v-for="broad in accountData.credit">
          <template v-for="middle in broad.children">
            <Row
              v-for="(account, index) in middle.children"
              ref="row"
              :key="account.id"
              :editable="editable"
              :middle="middle"
              :index="index"
              :account="account"
              :total="creditTotal"
              :has-new-row="canAddRow && editable && middle.type!=='fixed'"
              :class="rowClass(account)"
              @moveAccount="moveAccount"
              @moveAmount="moveAmount"
              @change="onRowchange"
            />
            <NewRow
              v-if="canAddRow && editable && middle.type!=='fixed'"
              ref="row"
              :key="broad.name + middle.name + 'new'"
              :header="middle.children.length > 0 ? false : middle.name"
              :middle="middle"
              @moveAccount="moveAccount"
              @moveAmount="moveAmount"
              @change="onRowchange"
            />
          </template>
        </template>
        <tr class="sum-row">
          <th colspan="2">
            合計
          </th>
          <td>{{ format(creditTotal) }}</td>
          <td />
          <td v-if="editable" />
        </tr>
        <template v-for="broad in accountData.debit">
          <template v-for="middle in broad.children">
            <Row
              v-for="(account, index) in middle.children"
              ref="row"
              :key="account.id"
              :editable="editable"
              :middle="middle"
              :index="index"
              :account="account"
              :total="debitTotal"
              :has-new-row="canAddRow && editable && middle.type!=='fixed'"
              :class="rowClass(account)"
              @moveAccount="moveAccount"
              @moveAmount="moveAmount"
              @change="onRowchange"
            />
            <NewRow
              v-if="canAddRow && editable && middle.type!=='fixed'"
              ref="row"
              :key="broad.name + middle.name + 'new'"
              :header="middle.children.length > 0 ? false : middle.name"
              :middle="middle"
              @moveAccount="moveAccount"
              @moveAmount="moveAmount"
              @change="onRowchange"
            />
          </template>
        </template>
        <tr class="sum-row">
          <th colspan="2">
            合計
          </th>
          <td>{{ format(debitTotal) }}</td>
          <td />
          <td v-if="editable" />
        </tr>
      </tbody>
    </table>
    <table class="footer">
      <tfoot>
        <tr>
          <th>
            検算結果
          </th>
          <td>
            {{ format(creditTotal - debitTotal) }}
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</template>

<script>
import Row from './Row';
import NewRow from './NewRow';
import { mapState, mapGetters } from "vuex";

const formatter = new Intl.NumberFormat('ja-JP');

export default {
  components: { Row, NewRow },
  props: {
    editable: { type: Boolean, default: true },
  },
  computed: {
    ...mapState({
      accountData: state => state.current.accountData,
      creditTotal: state => state.current.accountData.creditTotal,
      debitTotal: state => state.current.accountData.debitTotal,
    }),
    ...mapGetters('current', ['canAddRow']),
  },
  methods: {
    moveAccount(event) {
      const elements = document.getElementsByClassName('InputAccount');
      const index = [].findIndex.call(elements, e => e === event.target.$refs.account.$el)
      const move = event.key === 'ArrowUp' ? -1 : 1
      if (index + move >= 0 && index + move < elements.length) {
        elements[index + move].getElementsByTagName('input')[0].focus();
      }
    },
    moveAmount(event) {
      const elements = document.getElementsByClassName('InputAmount');
      const index = [].findIndex.call(elements, e => e === event.target.$refs.amount.$el)
      const move = event.key === 'ArrowUp' ? -1 : 1
      if (index + move >= 0 && index + move < elements.length) {
        elements[index + move].getElementsByClassName('static')[0].click();
      }
    },
    onRowchange(e) {
      //  別の行を変更したとき、バリデーションエラーになっている行が再チェック・確定されるようにする
      this.$refs.row.forEach(r => {
        if (r !== e && r.middle === e.middle) {
          r.update()
        }
      })
    },
    format(v) {
      return formatter.format(v)
    },
    rowClass(account) {
      return {
        'acc-row': account.type==='fixed',
        'sync1-row': (account.type === 'src' || account.type === 'dest') && account.name === '割引手形',
        'sync2-row': (account.type === 'src' || account.type === 'dest') && account.name === '裏書手形',
      }
    },
  }
};
</script>

<style lang="scss">
@import 'resources/sass/_variables.scss';

.Sheet {
  table {
    color: $text-color-base;
    border-collapse: collapse;
    empty-cells: show;
    vertical-align: middle;
    line-height: normal;
    width: 100%;
  }
  thead tr {
    background-color: $table-header-color;
  }
  tfoot tr {
    background-color: $table-footer-color;
  }
  th,td {
    border-top: solid 1px $table-border-color;
    border-bottom: solid 1px $table-border-color;
    padding: 3px;
  }
  col:nth-child(1) {
    width: 162px;
  }
  col:nth-child(n+2):nth-child(-n+4) {
    width: 132px;
  }
  col:nth-child(5) {
    width: 60px;
  }
  td:nth-last-child(n+1):nth-last-child(-n+3) {
    text-align: right;
  }
  .acc-row {
    background: $table-accrow-color;
    color: $table-accrow-text-color;
  }
  .sync1-row td {
    background: #e3ecf8;
  }
  .sync2-row td {
    background: #e4f3d3;
  }
  .sum-row {
    background: $table-sumrow-color;
  }
  .cell {
    writing-mode: vertical-rl;
  }
  .footer {
    margin-top: 15px;
    th {
      width: 552px;
    }
    td {
      padding-right: 53px;
    }
  }
}
</style>
