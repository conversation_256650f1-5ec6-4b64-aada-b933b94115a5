<template>
  <div class="ButtonArea">
    <slot name="left" />
    <Button
      type="primary"
      :disabled="disableChart"
      @click="$emit('show-chart')"
    >
      グラフ表示
    </Button>
    <slot name="right" />
  </div>
</template>

<script>
import { Button } from 'element-ui';

export default {
  components: { Button },
  props: {
    disableChart: { type: Boolean, required: true },
  }
}
</script>

<style lang="scss">
.TemplateSheet .ButtonArea {
  display: flex;
  justify-content: flex-end;
  padding: 10px 30px;
}
</style>
