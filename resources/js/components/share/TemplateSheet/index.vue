<template>
  <div class="TemplateSheet">
    <Alert
      v-if="!sheetCanAdd"
      type="error"
      show-icon
      :closable="false"
      title="キャッシュ体質図チェック"
      description="キャッシュ体質図の登録数が最大になっています。新しくキャッシュ体質図を登録することができません。"
    />
    <Alert
      v-if="!isValidChart"
      type="error"
      show-icon
      :closable="false"
      title="キャッシュ体質図チェック"
      description="構成図を表現できないマイナス値があります。"
    />
    <!-- <Alert
      v-if="!isValidLimit"
      type="error"
      show-icon
      :closable="false"
      title="キャッシュ体質図チェック"
      description="1.95%を超える値がないため構成図を表示できません。"
    /> -->
    <Alert
      v-if="!isBalance"
      type="error"
      show-icon
      :closable="false"
      title="キャッシュ体質図チェック"
      description="借方と貸方の合計額が一致しないので構成図を表示できません。"
    />
    <Alert
      v-if="!canAddRow"
      type="info"
      show-icon
      :closable="false"
      title="キャッシュ体質図チェック"
      description="追加できる科目は200個までです。これ以上科目を追加できません。"
    />
    <slot name="form" />
    <ButtonArea
      :disable-chart="disableChart"
      @show-chart="onShowChart"
    >
      <template v-slot:left>
        <slot name="button-area-left" />
      </template>
      <template v-slot:right>
        <slot name="button-area-right" />
      </template>
    </ButtonArea>
    <Sheet
      ref="sheet"
      :key="sheetData.key"
      :editable="editable"
    />
    <ButtonArea
      :disable-chart="disableChart"
      @show-chart="onShowChart"
    >
      <template v-slot:left>
        <slot name="button-area-left" />
      </template>
      <template v-slot:right>
        <slot name="button-area-right" />
      </template>
    </ButtonArea>
    <Dialog
      :visible.sync="chartVisible"
      top="20px"
      width="762px"
    >
      <div class="button-area">
        <UnitSelect
          v-model="unit"
          :options="units"
        />
        <Button @click="onClickPdf">
          PDF出力
        </Button>
      </div>
      <ChartConstitution
        ref="chart"
        :chart-visible="chartVisible"
        :sheet-data="sheetData"
        :filterd-account-data="filteredAccount"
        :constitution-data="constitution"
        :unit="units[unit]"
      />
      <form
        ref="pdf"
        method="post"
        :action="(isManage ? '/manage' : '') + '/pdf/constitution'"
      >
        <input
          type="hidden"
          name="_token"
          :value="csrfToken"
        >
        <input
          type="hidden"
          name="svg"
          :value="svg"
        >
        <input
          type="hidden"
          name="leftTitle1"
          :value="$refs['chart'] && $refs['chart'].leftTitle1"
        >
        <input
          type="hidden"
          name="leftTitle2"
          :value="$refs['chart'] && $refs['chart'].leftTitle2"
        >
        <input
          type="hidden"
          name="rightTitle"
          :value="$refs['chart'] && $refs['chart'].rightTitle"
        >
        <input
          type="hidden"
          name="unit"
          :value="units[unit].label"
        >
      </form>
    </Dialog>
  </div>
</template>

<script>
import ChartConstitution from '../ChartConstitution';
import Sheet from "../Sheet";
import ButtonArea from './ButtonArea';
import UnitSelect from '../../parts/UnitSelect';
import { Button, Dialog, Alert } from 'element-ui';
import { mapState, mapGetters } from "vuex";

export default {
  components: { ButtonArea, Sheet, ChartConstitution, UnitSelect, Button, Dialog, Alert },
  props: {
    isManage: { type: Boolean, default: false },
    editable: { type: Boolean, default: true },
  },
  data() {
    return {
      chartVisible: false,
      svg: null,
    }
  },
  computed: {
    ...mapState(["csrfToken"]),
    ...mapState({ sheetData: state => state.current }),
    ...mapGetters(["sheetCanAdd"]),
    ...mapGetters('current', [
      "canAddRow", "filteredAccount", "constitution", "isValidChart", "isBalance", "disableChart"
    ]),
    ...mapGetters('ui', ["units"]),
    unit: {
      get() {
        return this.$store.state.ui.unit
      },
      set(value) {
        this.$store.commit('ui/updateUnit', value)
      }
    },
  },
  methods: {
    onShowChart() {
      this.chartVisible = true;
    },
    onClickPdf() {
      this.svg = this.$refs['chart'].getSvg();
      this.$nextTick(function() {
        this.$refs['pdf'].submit();
      })
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';
@import 'resources/sass/_mixin.scss';

.TemplateSheet {
  .el-alert {
    @include content-width(25px, 25px);
  }
  .ButtonArea {
    @include content-width(10px, 22px);
    background-color: $button-area-color;
  }
  .el-dialog {
    .button-area {
      @include button-area-pdf;
    }
  }
}
</style>
