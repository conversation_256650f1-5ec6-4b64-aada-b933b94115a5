<template>
  <div class="ChartConstitution">
    <div class="header">
      <span>{{ leftTitle1 }}</span>
      <span>キャッシュ体質図</span>
      <span>{{ rightTitle }}</span>
    </div>
    <div class="header">
      <span style="margin-left: 1em;">{{ leftTitle2 }}</span>
      <span>&nbsp;</span>
      <span class="unitDisp">(単位：{{ unit.label }})</span>
    </div>
    <div
      id="constitution"
      ref="chart"
      class="chart"
    />
    <div class="CompanyName">
      {{ user.show_pdf_company_name ? user.company_name : '' }}
    </div>
  </div>
</template>

<script>
import * as charts from "../../../charts";
import { mapState } from "vuex";

export default {
  props: {
    chartVisible: { type: [Boolean, Number] , required: true },
    sheetData: { type: Object, required: true },
    filterdAccountData: { type: Object, required: true },
    constitutionData: { type: Object, required: true },
    unit: { type: Object, required: true },
  },
  computed: {
    ...mapState(["user"]),
    leftTitle1() {
      return this.sheetData.groupingName
    },
    leftTitle2() {
      return this.sheetData.dataName
    },
    rightTitle() {
      const date = new Date(this.sheetData.dataDate);
      // TELEGRAM-61 西暦に統一する
      // const str = date.toLocaleDateString('ja-JP-u-ca-japanese', { era: 'long', year: 'numeric', month: 'long'});
      // if (date >= new Date('2019/05/01') && str.indexOf('平成') !== -1) {
      //   return `令和${date.getFullYear()-2018}年${date.getMonth()+1}月`
      // }
      // return str;
      return `${date.getFullYear()}年${date.getMonth()+1}月`
    },
  },
  watch: {
    chartVisible: function(val) {
      if (val) {
        this.drawChart()
      }
    },
    unit: function() {
      this.drawChart()
    },
  },
  mounted() {
    this.drawChart()
  },
  methods: {
    getSvg() {
      return this.$refs['chart'].innerHTML;
    },
    drawChart() {
      charts.constitution(
        this.filterdAccountData,
        this.constitutionData,
        this.user,
        this.unit.value
      )
    }
  }
}
</script>

<style lang="scss">
.ChartConstitution {
  .header {
    display: flex;
    width: 720px;
    justify-content: center;
    align-items: flex-start;
    font-size: 1.2em;
    span {
      flex-basis: 200px;
      &:nth-child(2n+1) {
        flex-grow: 1;
      }
      &:nth-child(2) {
        text-align: center;
      }
      &:nth-child(3) {
        text-align: right;
      }
    }
  }
  .unitDisp {
    font-size: 0.9rem;
    margin-top: 0.4rem;
  }
  .CompanyName {
    width: 720px;
    margin-top: 0.5em;
    font-size: 1.4em;
    text-align: right;
  }
}
</style>
