<template>
  <div class="TemplateHistory">
    <Alert
      v-if="histories.length === 0"
      type="info"
      :closable="false"
      show-icon
    >
      履歴データがありません
    </Alert>
    <template v-if="histories.length > 0">
      <HistoryTabTable
        @tab-click="setSelectedGroup($event)"
        @selection-change="handleSelectionChange"
      >
        <template v-slot="scope">
          <slot
            name="table-button-area"
            :row="scope.row"
          />
        </template>
      </HistoryTabTable>
      <div class="button-area">
        <Button
          type="primary"
          :disabled="!multipleSelection[selectedGroup] || multipleSelection[selectedGroup].length === 0"
          @click="handleTransitionClick"
        >
          推移表示
        </Button>
      </div>
      <Dialog
        :visible.sync="chartVisible"
        top="20px"
        width="760px"
      >
        <div class="button-area">
          <UnitSelect
            v-model="unit"
            :options="units"
          />
          <Button @click="onClickPdf">
            PDF出力
          </Button>
        </div>
        <ChartTransition
          ref="chart"
          :chart-visible="chartVisible"
          :transition="multipleSelection[selectedGroup]"
          :unit="units[unit]"
          @click="showConstruction"
        />
        <form
          ref="form"
          method="post"
          :action="(isManage ? '/manage' : '') + '/pdf/transition'"
        >
          <input
            type="hidden"
            name="_token"
            :value="csrfToken"
          >
          <input
            type="hidden"
            name="transition"
            :value="svg"
          >
          <input
            type="hidden"
            name="leftTitle"
            :value="selectedGroup"
          >
          <input
            type="hidden"
            name="rightTitle"
            value=""
          >
          <input
            type="hidden"
            name="unit"
            :value="units[unit].label"
          >
        </form>
      </Dialog>
      <Dialog
        :visible.sync="innerVisible"
        top="20px"
        width="850px"
        class="construction"
      >
        <Button
          type="primary"
          icon="el-icon-arrow-left"
          circle
          :disabled="constructionIndex===0"
          @click="moveConstruction(-1)"
        />
        <ChartConstitution
          v-if="constructionId"
          ref="chart"
          :key="constructionId"
          :chart-visible="constructionIndex+1"
          :sheet-data="constructionData"
          :filterd-account-data="constructionData.filteredAccountData"
          :constitution-data="constructionData.constitutionData"
          :unit="units[unit]"
        />
        <Button
          type="primary"
          icon="el-icon-arrow-right"
          circle
          :disabled="currentGroup && constructionIndex+1===currentGroup.length"
          @click="moveConstruction(1)"
        />
      </Dialog>
    </template>
  </div>
</template>

<script>
import HistoryTabTable from '../HistoryTabTable';
import ChartTransition from '../ChartTransition';
import ChartConstitution from '../ChartConstitution';
import UnitSelect from '../../parts/UnitSelect';
import { Dialog, Alert, Button } from 'element-ui';
import { mapState, mapGetters, mapMutations } from "vuex";

export default {
  components: {
    HistoryTabTable, ChartTransition, ChartConstitution, UnitSelect,
    Dialog, Alert, Button
  },
  props: {
    isManage: { type: Boolean, default: false },
  },
  data() {
    return {
      multipleSelection: {},
      chartVisible: false,
      innerVisible: false,
      constructionId: 0,
      constructionIndex: 0,
      svg: null,
      unit: 0, // 単位index指定
    }
  },
  computed: {
    ...mapState(["histories", "csrfToken"]),
    ...mapState('ui', ["selectedGroup"]),
    ...mapGetters('ui', ["units"]),
    currentGroup() {
      return this.multipleSelection[this.selectedGroup];
    },
    constructionData() {
      return this.currentGroup[this.constructionIndex];
    },
  },
  methods: {
    ...mapMutations('ui', ['setSelectedGroup']),
    handleSelectionChange(val) {
        this.$set(this.multipleSelection, this.selectedGroup, val);
    },
    handleTransitionClick() {
      this.chartVisible = true;
    },
    showConstruction(id) {
      this.constructionId = id;
      this.constructionIndex = this.currentGroup.findIndex(e => e.id === id)
      this.innerVisible = true;
    },
    moveConstruction(val) {
      this.constructionIndex += val;
    },
    onClickPdf() {
      this.svg = this.$refs['chart'].getSvg();
      this.$nextTick(function() {
        this.$refs['form'].submit();
      })
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';
@import 'resources/sass/_mixin.scss';

.TemplateHistory {
  .el-alert {
    @include content-width(25px, 25px);
  }
  .HistoryTabTable {
    @include content-width(25px, 25px);
  }
  >.button-area {
    @include content-width(25px, 25px);
    display: flex;
    justify-content: flex-end;
    background-color: $button-area-color;
    padding: 10px 30px;
  }
  .el-dialog {
    .button-area {
      @include button-area-pdf;
    }
  }
  .construction {
    .el-dialog__body {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
