<template>
  <div class="ManageInput">
    <h3 v-if="'' != userData.id">
      ユーザー詳細編集
    </h3>
    <h3 v-else>
      ユーザー新規登録
    </h3>
    <Form
      ref="user-form"
      method="post"
      action="/manage"
      :model="userData"
      :rules="rules"
      label-width="160px"
    >
      <input
        type="hidden"
        name="user_id"
        :value="userData.id"
      >
      <input
        type="hidden"
        name="_token"
        :value="token"
      >
      <FormItem
        label="企業名："
        prop="company_name"
      >
        <Input
          v-model="userData.company_name"
          name="company_name"
          maxlength="255"
        />
      </FormItem>
      <FormItem
        label="名前："
        prop="user_name"
      >
        <Input
          v-model="userData.user_name"
          name="user_name"
          maxlength="255"
        />
      </FormItem>
      <FormItem
        label="アカウントID："
        prop="account_id"
      >
        <Input
          v-model="userData.account_id"
          name="account_id"
          maxlength="255"
        />
      </FormItem>
      <FormItem
        label="パスワード："
        prop="password"
      >
        <Input
          v-model="userData.password"
          name="password"
          style="width: 60%;"
          maxlength="16"
        />
        <Button
          type="primary"
          @click="genPassword()"
        >
          生成
        </Button>
      </FormItem>
      <FormItem
        label="利用開始日："
        prop="start_at"
      >
        <DatePicker
          v-model="userData.start_at"
          name="start_at"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </FormItem>
      <FormItem
        label="利用期限日："
        prop="end_at"
      >
        <DatePicker
          v-model="userData.end_at"
          name="end_at"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </FormItem>
      <FormItem
        label="グラフカラー："
        prop="mst_theme_id"
      >
        <Input
          v-model="userData.mst_theme_id"
          name="mst_theme_id"
          style="width: 60%;"
          :readonly="true"
        />
        <Button
          type="primary"
          @click="dialogGraph = true;"
        >
          変更
        </Button>
      </FormItem>
      <FormItem
        label="上場情報リンク："
        prop="show_link"
      >
        <Radio
          v-model="userData.show_link"
          name="show_link"
          label="1"
        >
          ON
        </Radio>
        <Radio
          v-model="userData.show_link"
          name="show_link"
          label="0"
        >
          OFF
        </Radio>
      </FormItem>
      <FormItem
        label="企業名表示"
        prop="show_pdf_company_name"
      >
        <Radio
          v-model="userData.show_pdf_company_name"
          name="show_pdf_company_name"
          label="1"
        >
          ON
        </Radio>
        <Radio
          v-model="userData.show_pdf_company_name"
          name="show_pdf_company_name"
          label="0"
        >
          OFF
        </Radio>
      </FormItem>
      <FormItem
        label="体質図作成数"
        prop="max_input_sheets"
      >
        <Select
          v-model="userData.max_input_sheets"
        >
          <Option
            v-for="option in options"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </Select>
        <input
          type="hidden"
          name="max_input_sheets"
          :value="userData.max_input_sheets"
        >
      </FormItem>
      <Dialog
        width="35%"
        :visible.sync="dialogGraph"
        center
      >
        <table>
          <tbody>
            <tr
              v-for="theme in themes"
              :key="theme.id"
            >
              <td style="padding:0 5px;">
                {{ theme.id }}
              </td>
              <td
                style="width:50px; border:solid 1px #999999;"
                :style="setBackGroundColor(theme.color_1)"
              />
              <td
                style="width:50px; border:solid 1px #999999;"
                :style="setBackGroundColor(theme.color_2)"
              />
              <td
                style="width:50px; border:solid 1px #999999;"
                :style="setBackGroundColor(theme.color_3)"
              />
              <td
                style="width:50px; border:solid 1px #999999;"
                :style="setBackGroundColor(theme.color_4)"
              />
              <td>
                <Button @click="selectTheme(theme.id)">
                  決定
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <span slot="footer">
          <Button
            type="primary"
            @click="dialogGraph = false;"
          >
            閉じる
          </Button>
        </span>
      </Dialog>

      <div class="fotter">
        <span>
          <Button
            v-if="'' != userData.id"
            @click="openUrl('/manage/history/' + userData.id)"
          >
            キャッシュ体質図履歴
          </Button>
        </span>
        <Button
          type="primary"
          @click="save()"
        >
          決定
        </Button>
        <Button @click="cancelEvent()">
          戻る
        </Button>
      </div>
    </Form>
  </div>
</template>

<style>
</style>

<script>
import { Form, FormItem, Input, DatePicker, Radio, Select, Option, Button, Dialog, Loading } from 'element-ui';
import axios from "axios";

export default {
  components: { Form, FormItem, Input, DatePicker, Radio, Select, Option, Button, Dialog },
  props: {
    userData: {type: Object, required: true },
    themes: {type: Array, required: true },
    token: {type: String, required: true }
  },
  data() {
    // アカウントIDバリデーション
    var validateAccountId = (rule, value, callback) => {
      var literal = /^[a-zA-Z0-9.\-_@]+$/;
      if (!literal.test(value)) {
        callback(new Error('アカウントIDには半角英数字、.-_@のみ入力してください'));
      } else if (this.checkAccountId(value) === false) {
        callback(new Error('入力されたアカウントIDは既に存在しています'));
      }
      callback();
    };
    // パスワードバリデーション
    var validatePassword = (rule, value, callback) => {
      var literal = /^[a-zA-Z0-9]+$/;
      if (!literal.test(value)) {
        callback(new Error('パスワードには半角英数字のみ入力してください'));
      }
      callback();
    };
    // 利用開始日バリデーション
    var validateStartAtIsBeforeEndAt = (rule, value, callback) => {
      if (value > this.userData.end_at) {
        callback(new Error('利用開始日には利用期限日より前の日付を選択してください'));
      }
      callback();
    };
    // 利用期限日バリデーション
    var validateEndAtIsAfterStartAt = (rule, value, callback) => {
      if (value <= this.userData.start_at) {
        callback(new Error('※利用期限日は利用開始日より後の日付を選択してください'));
      }
      callback();
    };

    return {
      dialogGraph: false,
      userDatas: '',
      rules: {
        company_name: [
          { required: true, message: '企業名を入力してください', trigger: 'change' },
          { max: 255, message: '255文字以内で入力してください' },
        ],
        user_name: [
          { required: true, message: '名前を入力してください', trigger: 'change' },
          { max: 255, message: '255文字以内で入力してください' },
        ],
        account_id: [
          { required: true, message: 'アカウントIDを入力してください', trigger: 'change' },
          { max: 255, message: '255文字以内で入力してください' },
          { validator: validateAccountId },
        ],
        password: [
          { required: true, message: 'パスワードを入力してください', trigger: 'change' },
          { max: 16, message: '16文字以内で入力してください' },
          { validator: validatePassword },
        ],
        start_at: [
          { required: true, message: '利用開始日を選択してください', trigger: 'change' },
          { validator: validateStartAtIsBeforeEndAt },
        ],
        end_at: [
          { required: true, message: '利用期限日を選択してください', trigger: 'change' },
          { validator: validateEndAtIsAfterStartAt },
        ],
      },
    };
  },
  computed: {
    options() {
      const max = 500
      const append = 20
      let options = [...Array(max / 20)].map((d, idx) => {
        const value = (idx + 1) * append
        return { lable: value, value }
      })
      options.push({ label: "無制限", value: 0 })
      return options
    }
  },
  created () {
    var url = '';
    if (this.userData.id == '') {
      url = '/api/user/';
    } else {
      url = '/api/user/' + this.userData.id;
    }

    axios.get(url)
      .then(res => {
        this.userDatas = res.data.userDatas;
      })
      .catch(() => {
        console.log('API error.');
        // TODO: エラーメッセージ表示
      });
  },
  methods: {
    genPassword(length = 12) {
      let password_base = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
      let password = '';
      for (let i = 0; i < length; i++) {
          password += password_base.charAt(Math.floor(Math.random() * password_base.length));
      }
      this.userData.password = password;
    },
    setBackGroundColor(color) {
      return {'background-color': color, 'width': '50px'}
    },
    selectTheme(id) {
      this.userData.mst_theme_id = id;
      this.dialogGraph = false;
    },
    save() {
      this.$refs['user-form'].validate((valid) => {
        if (valid) {
          Loading.service({ fullscreen: true, text: 'Saving...', lock: true });
          this.$refs['user-form'].$el.submit();
        } else {
          return false;
        }
      });
    },
    cancelEvent() {
      this.$emit('cancel');
    },
    checkAccountId(accountId) {
      var duplicateUserData = this.userDatas.filter(userData => userData.account_id == accountId);
      if (duplicateUserData == '') {
        // アカウントIDの重複なし
        return true;
      } else {
        // アカウントIDの重複あり
        return false;
      }
    },
    openUrl(url) {
      window.open(url, '_blank')
    }
  }
};
</script>

<style lang="scss">
@import 'resources/sass/manage/variables';

.ManageInput {
  width: 75%;
  margin: 0 auto;

  table {
    margin: auto;
  }

  .fotter {
    display: flex;
    justify-content: flex-end;
    background-color:#e5e9f2;
    padding: 10px 30px;
    >span {
      flex-grow: 1;
    }
  }
}
</style>
