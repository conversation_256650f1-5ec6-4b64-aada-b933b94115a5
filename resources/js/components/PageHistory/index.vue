<template>
  <TemplateHisotry class="PageHistory">
    <template v-slot:table-button-area="scope">
      <Tooltip content="編集">
        <Button
          size="mini"
          circle
          icon="el-icon-edit"
          @click="editInputSheet({ id: scope.row.id })"
        />
      </Tooltip>
      <Tooltip content="コピー">
        <Button
          size="mini"
          circle
          icon="el-icon-document-copy"
          @click="copyInputSheet({ id: scope.row.id })"
        />
      </Tooltip>
      <Tooltip
        content="削除"
        popper-class="delete"
      >
        <Popconfirm
          title="このデータを削除してよろしいですか？"
          confirm-button-text="はい"
          cancel-button-text="いいえ"
          :hide-icon="true"
          @onConfirm="onDeleteConfirm(scope.row.id)"
        >
          <Button
            slot="reference"
            size="mini"
            circle
            icon="el-icon-delete"
          />
        </Popconfirm>
      </Tooltip>
    </template>
  </TemplateHisotry>
</template>

<script>
import TemplateHisotry from '../share/TemplateHistory';
import { Button, Popconfirm, Tooltip, Loading, MessageBox } from 'element-ui';
import { mapState, mapActions } from "vuex";

export default {
  components: { TemplateHisotry, Button, Popconfirm, Tooltip },
  computed: {
    ...mapState(["sample", "user", "histories"]),
  },
  methods: {
    ...mapActions(["copyInputSheet", "editInputSheet"]),
    onDeleteConfirm(id) {
      let loadingInstance = Loading.service({ fullscreen: true, text: 'Loading', lock: true });
      this.$store.dispatch('deleteInputSheet', { id })
      .then(() => {
        loadingInstance.close();
      })
      .catch(() => {
        loadingInstance.close();
        MessageBox.alert('データの削除処理でエラーが発生しました。', 'エラー', {
          type: 'error',
          center: true
        });
      })
    },
  }
}
</script>

<style lang="scss">
.PageHistory {
  .el-button+span {
    .el-button.el-popover__reference {
      margin-left: 10px;
    }
  }
}
.el-tooltip__popper.delete {
  margin-top: 17px;
}
</style>
