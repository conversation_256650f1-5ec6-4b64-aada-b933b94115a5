<template>
  <div class="ManagePageSheet">
    <TemplateSheet
      :is-manage="true"
      :editable="false"
    >
      <template v-slot:form>
        <dl class="form">
          <div><dt>企業名</dt><dd>{{ sheetData.groupingName }}</dd></div>
          <div><dt>対象月</dt><dd>{{ sheetData.dataDate }}</dd></div>
          <div><dt>タイトル</dt><dd>{{ sheetData.dataName }}</dd></div>
          <div>
            <dt>&nbsp;</dt>
            <dd>
              <Checkbox :value="sheetData.capitalizeDebtFromPresident">
                グラフの役員借入金を資本とする
              </Checkbox>
            </dd>
          </div>
        </dl>
      </template>
    </TemplateSheet>
  </div>
</template>

<script>
import TemplateSheet from '../share/TemplateSheet';
import { Checkbox } from 'element-ui';

export default {
  components: { TemplateSheet, Checkbox },
  props: {
    sheetData: { type: Object, required: true },
    user: { type: Object, required: true },
    token: { type: String, required: true },
  },
  methods: {
    onShowChart() {
      this.chartVisible = true;
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';
@import 'resources/sass/_mixin.scss';

.ManagePageSheet {
  .el-alert {
    @include content-width(25px, 25px);
  }
  .Sheet {
    @include content-width
  }
  .ButtonArea {
    @include content-width(10px, 22px);
    background-color: $button-area-color;
    padding: 10px 30px;
  }
  .form {
    @include content-width(25px, 25px);
    color: $text-color-base;
    div {
     display: flex;
     padding: 10px 0;
    }
    dt {
      width: 80px;
      padding-right: 12px;
      text-align: right;
    }
  }
}
</style>
