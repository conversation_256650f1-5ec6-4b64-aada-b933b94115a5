<template>
  <div class="wrapper">
    <div class="inner">
      <div
        class="logoBox"
        style="margin-bottom:30px;"
      >
        <ElLink
          href="http://www.triplewinlab.co.jp"
          style="display:block; margin-bottom:15px;"
          :underline="false"
        >
          <img
            :src="'/images/logo.png'"
            width="280px"
            style="margin:0 auto;"
            alt="TripleWin研究所"
          >
        </ElLink>
        <ElLink
          href="http://www.triplewinlab.co.jp"
          style="text-decoration:none; color:#000;"
          :underline="false"
        >
          http://www.triplewinlab.co.jp
        </ElLink>
      </div>
      <Alert
        v-if="expired == true"
        type="error"
        show-icon
        :closable="false"
        description="利用期間外です"
      />
      <div class="formBox">
        <Form
          ref="login-form"
          method="post"
          label-width="120px"
          :action="route"
          :model="form"
          class="loginForm"
          :class="{ manageStyle: isManage, userStyle: isUser }"
        >
          <input
            type="hidden"
            name="_token"
            :value="token"
          >
          <FormItem
            label="アカウントID："
            prop="account_id"
            :class="{ 'is-error': isAccountIdError }"
          >
            <Input
              v-model="form.account_id"
              name="account_id"
              type="text"
              autofocus="true"
              autocomplete="off"
            />
            <div
              v-if="isAccountIdError === true"
              class="el-form-item__error"
            >
              {{ accountIdErrorMsg }}
            </div>
          </FormItem>
          <FormItem
            label="パスワード："
            prop="password"
            :class="{ 'is-error': isPasswordError }"
          >
            <Input
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="off"
            />
            <div
              v-if="isPasswordError === true"
              class="el-form-item__error"
            >
              {{ passwordErrorMsg }}
            </div>
          </FormItem>
          <FormItem
            class="loginBtnFromItem"
          >
            <Button
              type="primary"
              @click="login"
            >
              ログイン
            </Button>
          </FormItem>
        </Form>
      </div>
      <div class="recommendText">
        当サービスを快適にご利用いただくために、以下の環境でのご利用を推奨いたします。
      </div>
      <div class="recommendBox">
        <div class="OSBox leftBox">
          <div class="OSName">
            Windows OS
          </div>
          <div class="browserName">
            ・Google Chrome 最新版<br>
            ・Microsoft Edge 最新版
          </div>
        </div>
        <div class="OSBox rightBox">
          <div class="OSName">
            Mac OS
          </div>
          <div class="browserName">
            ・Google Chrome 最新版<br>
            ・Safari 最新版<br>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Form, FormItem, Input, Button, Link, Alert, Loading } from 'element-ui';

export default {
  components: { Form, FormItem, Input, Button, ElLink: Link, Alert },
  props: {
    loginType: { type: String, required: true },
    route: { type: String, required: true },
    error: { type: String, required: false, 'default': '' },
    token: { type: String, required: true }
  },
  data () {
    return {
      form: {
        account_id: '',
        password: '',
      },
      isManage: false,
      isUser: false,
      isAccountIdError: false,
      isPasswordError: false,
      accountIdErrorMsg: '',
      passwordErrorMsg: '',
      expired: false,
      isFirst: true,
    }
  },
  created () {
    // safariの戻るでログイン画面へ遷移するとき対策
    window.onpageshow = () => {
      if (!this.isFirst) {
        Loading.service().close();
      }
    }

    // デザイン切替
    if (this.loginType == 'manage') {
      this.isManage = true;
    } else {
      this.isUser = true;
    }

    // エラー表示
    var errorMsgs = JSON.parse(this.error);
    if (errorMsgs.length != 0) {
      if (errorMsgs.account_id != undefined) {
        // アカウントID入力欄
        this.isAccountIdError  = true;
        this.accountIdErrorMsg = errorMsgs.account_id[0];
      }
      if (errorMsgs.password != undefined) {
        // パスワード入力欄
        this.isPasswordError  = true;
        this.passwordErrorMsg = errorMsgs.password[0];
      }
      if (errorMsgs.expired != undefined) {
        // ユーザー利用期限切れ
        this.expired = true;
      }
    }
  },
  methods: {
    login() {
      Loading.service({ fullscreen: true, text: 'Login...', lock: true });
      this.isFirst = false;
      this.$refs['login-form'].$el.submit();
    },
  }
};
</script>

<style lang="scss">
@import 'resources/sass/login';

.recommendText{
  margin-bottom: 20px;
  font-size: 15px;
  color: #606266;
}

.recommendBox{
  display: flex;
}

.OSBox{
  border-radius: 10px;
  background-color: #cccccc;
  border:solid 2px #356d3c;
  flex: 1 1 auto;
}

.recommendBox .leftBox{ margin-right: 8px; }
.recommendBox .rightBox{ margin-left: 8px; }

.OSName{
  border-radius: 10px 10px 0 0;
  background-color: #356d3c;
  padding: 10px;
  font-size: 15px;
  color: #fff;
}

.browserName{
  border-radius: 0 0 10px 10px;
  background-color: #fff;
  padding: 10px;
  text-align: left;
  line-height: 22px;
  font-size: 15px;
  color: #606266;
}
</style>
