<template>
  <Form
    class="UnitSelect"
    :inline="true"
  >
    <FormItem label="単位：">
      <Select
        :value="value"
        placeholder=""
        @input="$emit('input', $event)"
      >
        <Option
          v-for="(item, index) in options"
          :key="item.value"
          :label="item.label"
          :value="index"
        />
      </Select>
    </FormItem>
  </Form>
</template>

<script>
import { Select, Option, Form, FormItem } from 'element-ui';
export default {
  components: { Select, Option, Form, FormItem },
  props: {
    options: { type: Array, required: true },
    value: { type: Number, required: true },
  },
}
</script>

<style lang="scss">
.UnitSelect {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-select {
    width: 120px;
  }
}
</style>
