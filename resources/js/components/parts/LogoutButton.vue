<template>
  <div>
    <MenuItem
      index="logout"
      style="float: right;"
      @click="handleLogout"
    >
      <form
        ref="logout-form"
        :action="action"
        method="post"
      >
        <input
          type="hidden"
          name="_token"
          :value="token"
        >
        <Button
          @click="handleLogout"
        >
          ログアウト
        </Button>
      </form>
    </MenuItem>
  </div>
</template>

<script>
import Vue from "vue";
import { MenuItem, Button, Loading } from 'element-ui'

export default {
  components: { MenuItem, Button },
  props: {
    action: {type: String, required: true },
    token: {type: String, required: true }
  },
  data() {
    return {
      activeIndex: 'list',
    };
  },
  methods: {
    handleLogout() {
      Loading.service({ fullscreen: true, text: 'Loading', lock: true });
      this.$refs['logout-form'].submit();
    }
  }
};
</script>

<style lang="scss" scoped>
.el-menu-item:hover {
  background-color: transparent;
}
</style>
