<template>
  <Input
    ref="input"
    v-model="inputValue"
    class="InputAccount"
    maxlength="15"
    @keyup.enter.native="$emit('enter', $event)"
    @input="$emit('input', $event)"
    @blur="$emit('blur', $event)"
    @change="$emit('change', $event)"
  />
</template>

<script>
import { Input } from 'element-ui';

export default {
  components: { Input },
  props: {
    value: { type: String, required: true },
  },
  data() {
    return {
      inputValue: this.value,
    }
  },
  mounted() {
    this.$refs['input'].$refs['input'].addEventListener('keydown', (e => {
      if (["ArrowDown", "ArrowUp", "ArrowRight"].indexOf(e.key) > -1) {
        this.$emit('keydown', e.key)
        e.preventDefault()
      }
    }))
  },
  methods: {
    setValue(value) {
      this.inputValue = value;
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';

.InputAccount {
  input {
    border-color: transparent;
    line-height: 1em;
    height: calc(2em + 2px);
    padding: 5px;
    font-size: $font-size-base;
    &:hover {
      border-color: transparent;
    }
    &:focus {
      border-color: $el-color-primary;
    }
  }
}
</style>
