<template>
  <div
    ref="InputAmount"
    class="InputAmount"
    tabindex="0"
  >
    <div
      class="static"
      :style="{ visibility: editable ? 'hidden' : 'visible' }"
      @click="click"
    >
      {{ format(currentValue) }}
    </div>
    <Input
      ref="input"
      v-model="userInput"
      maxlength="20"
      tabindex="0"
      :style="{ visibility: editable ? 'visible' : 'hidden' }"
      @input="$emit('input', $event)"
      @change="handleChange"
      @keydown.enter.native="enter"
      @blur="blur"
    />
  </div>
</template>

<script>
import { Input } from 'element-ui';

const formatter = new Intl.NumberFormat('ja-JP');

export default {
  components: { Input },
  props: {
    value: { type: [Number, String], required: true },
    max: { type: Number, default: 999999999999999 },
    min: { type: Number, default: -999999999999999 },
  },
  data() {
    return {
      editable: false,
      currentValue: this.value,
      userInput: this.value,
    }
  },
  watch: {
    value: function(v) {
      this.setValue(v);
    }
  },
  mounted() {
    this.$refs['input'].$refs['input'].addEventListener('keydown', (e => {
      if (["ArrowDown", "ArrowUp", "ArrowLeft"].indexOf(e.key) > -1) {
        this.$emit('keydown', e.key)
        e.preventDefault()
      }
    }))
    this.$refs['InputAmount'].addEventListener('focus', () => {
      this.click()
    })
  },
  methods: {
    setValue(value) {
      this.currentValue = value;
      this.userInput = value;
    },
    click() {
      this.editable = true;
      this.$nextTick(function () {
        this.$refs.input.focus();
        this.$refs.input.select();
      });
    },
    handleChange(newVal) {
      const oldVal = this.currentValue;
      // 全角数字を半角数字に置き換える
      newVal = newVal.replace(/[０-９]/g, (s) => {
        return String.fromCharCode(s.charCodeAt(0) - 0xfee0)
      });
      // カンマは削除
      newVal = newVal.replace(/[,]/g, '')
      // マイナスっぽい記号をマイナスに置き換える
      newVal = newVal.replace(/[\u2010\u2011\u1013\u2014\u2015\u2212\u2211\u207b\u208b\u30FC\uFF0D\uFF70]/g, "-");
      newVal = Number(newVal)
      if (isNaN(newVal)) return;
      if (newVal >= this.max) newVal = this.max;
      if (newVal <= this.min) newVal = this.min;
      this.userInput = newVal;
      if (oldVal === newVal) return;
      this.currentValue = newVal;
      this.$emit('change', newVal);
    },
    enter() {
      // 次の数値入力へ移動する
      this.$emit('keydown', 'ArrowDown')
    },
    blur() {
      this.editable = false;
    },
    format(value) {
      return formatter.format(value);
    }
  }
}
</script>

<style lang="scss">
@import 'resources/sass/variables';

.InputAmount {
  position: relative;
  .static {
    position: absolute;
    width: 100%;
    line-height: calc(2em + 2px);
    text-align: right;
  }
  .el-input {
    line-height: calc(2em + 2px);
    width: 100%;
    input {
      font-size: $font-size-base;
      line-height: 1em;
      height: calc(2em + 2px);
      text-align: right !important;
      padding: 5px !important;
    }
  }
}
</style>
