<template>
  <div>
    <Menu
      ref="manage-menu"
      :default-active="activeIndex"
      mode="horizontal"
      @select="handleSelect"
    >
      <MenuItem index="list">
        ユーザー一覧
      </MenuItem>
      <MenuItem
        v-if="activeIndex==='create'"
        index="create"
      >
        ユーザー新規登録
      </MenuItem>
      <MenuItem
        v-if="activeIndex==='edit'"
        index="edit"
      >
        ユーザー詳細編集
      </MenuItem>
      <LogoutButton
        action="/manage/logout"
        :token="token"
      />
    </Menu>

    <ManageList
      v-if="activeIndex==='list'"
      :users="users"
      @select="parentSelect"
    />

    <ManageInput
      v-if="activeIndex==='edit' || activeIndex==='create'"
      :user-data="userData"
      :themes="themes"
      :token="token"
      @cancel="parentCancel"
    />

    <Dialog
      title="注意"
      width="30%"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <span>変更内容が保存されませんがよろしいですか？</span>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <Button @click="handleClose">Cancel</Button>
        <Button
          type="primary"
          @click="dialogResult = true; handleClose()"
        >OK</Button>
      </span>
    </Dialog>
  </div>
</template>

<script>
import { Button, Menu, MenuItem, Dialog } from 'element-ui'
import LogoutButton from './parts/LogoutButton';
import ManageList from './ManageList';
import ManageInput from './ManageInput';

export default {
  components: { Button, Menu, MenuItem, Dialog, LogoutButton, ManageList, ManageInput },
  props: {
    users: {type: Array, required: true },
    themes: {type: Array, required: true },
    initdate: {type: Object, required: true },
    index: {type: String, required: true },
    token: {type: String, required: true }
  },
  data() {
    return {
      activeIndex: this.index,
      dialogVisible: false,
      dialogResult: false,
      initData: {
        id : "",
        info_id : "info_",
        company_name : "",
        user_name : "",
        account_id : "",
        password : "",
        start_at : this.initdate.start_at,
        end_at : this.initdate.end_at,
        mst_theme_id : 1,
        show_link : "0",
        show_pdf_company_name: "1",
        max_input_sheets: 20,
      },
      backupUserData: null,
    };
  },
  methods: {
    handleSelect(key) {
      if ('list' == key && 'list' != this.activeIndex) {
        this.dialogVisible = true;
        this.$refs['manage-menu'].activeIndex = this.activeIndex;
      } else {
        this.activeIndex = key;
      }
    },
    handleClose() {
      if (this.dialogResult) {
        this.activeIndex = 'list';
        this.restoreUserData();
      }
      this.dialogResult = false;
      this.dialogVisible = false;
    },

    parentSelect(key, user) {
      if (user) {
        this.userData = user;
        this.backupUserData = {
          company_name: this.userData.company_name,
          user_name: this.userData.user_name,
          account_id: this.userData.account_id,
          password: this.userData.password,
          start_at: this.userData.start_at,
          end_at: this.userData.end_at,
          mst_theme_id: this.userData.mst_theme_id,
          show_link: this.userData.show_link,
          show_pdf_company_name: this.userData.show_pdf_company_name,
          max_input_sheets: this.userData.max_input_sheets,
        };
      } else {
        this.userData = this.initData;
        this.backupUserData = null;
      }
      this.activeIndex = key;
    },
    parentCancel() {
      this.dialogVisible = true;
    },
    restoreUserData() {
      if (this.backupUserData) {
        this.userData.company_name = this.backupUserData.company_name;
        this.userData.user_name = this.backupUserData.user_name;
        this.userData.account_id = this.backupUserData.account_id;
        this.userData.password = this.backupUserData.password;
        this.userData.start_at = this.backupUserData.start_at;
        this.userData.end_at = this.backupUserData.end_at;
        this.userData.mst_theme_id = this.backupUserData.mst_theme_id;
        this.userData.show_link = this.backupUserData.show_link;
        this.userData.show_pdf_company_name = this.backupUserData.show_pdf_company_name;
        this.userData.max_input_sheets = this.backupUserData.max_input_sheets;
        this.backupUserData = null;
      }
    }
  }
};
</script>

<style lang="scss">
@import 'resources/sass/normalize';

* {
  box-sizing: border-box !important;
  font-family: 'Hiragino Kaku Gothic Pro', 'メイリオ', 'Helvetica Neue', 'Arial', sans-serif !important;
}

.el-table .expired-row {
  background: lightgray;
}
</style>
