<template>
  <div
    v-if="state"
    class="ManageHistory"
  >
    <Menu
      :default-active="activeIndex"
      mode="horizontal"
      @select="handleSelectMenu"
    >
      <li class="menu-title">
        キャッシュ体質図履歴
      </li>
      <li class="menu-title">
        {{ user.company_name }}
      </li>
      <li class="menu-title">
        {{ user.user_name }}
      </li>
      <li
        v-if="activeIndex!=='history'"
        class="menu-title-button"
      >
        <Button @click="handleSelectMenu('history')">
          戻る
        </Button>
      </li>
    </Menu>
    <ManagePageHisotry
      v-show="activeIndex==='history'"
      key="history"
    />
    <ManagePageSheet
      v-if="current.id"
      v-show="activeIndex==='sheet'"
      :sheet-data="current"
      :user="user"
      :token="token"
    />
  </div>
</template>

<script>
import { <PERSON><PERSON>, But<PERSON>, <PERSON>ading } from 'element-ui';
import ManagePageHisotry from './ManagePageHistory';
import ManagePageSheet from './ManagePageSheet';
import { mapState } from "vuex";

export default {
  components: { Menu, Button, ManagePageHisotry, ManagePageSheet },
  props: {
    token: { type: String, required: true }
  },
  data() {
    return {
      state: null,
    }
  },
  computed: {
    ...mapState('ui', { activeIndex: state => state.activeIndex }),
    ...mapState(["sample", "user", "histories", "current"]),
    userId() {
      return location.pathname.split('/').pop()
    }
  },
  created() {
    let loadingInstance = Loading.service({ fullscreen: true, text: 'Loading', lock: true });
    this.$store.dispatch('initManage', { userId: this.userId }).finally(() => {
      this.$store.commit('csrfToken', this.token)
      this.state = 'init'
      loadingInstance.close();
    })
  },
  methods: {
    handleSelectMenu(key) {
      this.$store.commit('ui/activeIndex', key)
    },
  }
}
</script>

<style lang="scss">
@import 'resources/sass/_mixin.scss';

.ManageHistory {
  .el-menu {
    @include content-width
  }
  .menu-title, .menu-title-button {
    @include menu-item
  }
  .menu-title-button {
    float: right;
  }
}
</style>
