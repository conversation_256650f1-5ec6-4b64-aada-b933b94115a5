import { setId } from './index'

export default {
  init: (state, payload) => {
    state.user = payload.user
    state.sample = setId(JSON.parse(payload.sample))
    state.histories = payload.histories
    state.ui.selectedGroup =  state.histories[0] && state.histories[0].groupingName || '未設定'
  },
  csrfToken: (state, payload) => state.csrfToken = payload,
  deleteInputSheet: (state, payload) => {
    state.histories.splice(state.histories.findIndex(d => d.id === payload.id), 1);
  },
  pushInputSheet: (state, payload) => {
    state.histories.push(payload);
  },
  updateInputSheet: (state, payload) => {
    state.histories.splice(state.histories.findIndex(d => d.id === payload.id), 1, payload);
  },
}
