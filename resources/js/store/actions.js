import { RepositoryFactory } from '../repositories/RepositoryFactory'

// Debug environment configuration (development only)
if (process.env.NODE_ENV !== 'production') {
  console.debug('Environment variables:', {
    MIX_APP_REPOSITORY: process.env.MIX_APP_REPOSITORY,
    NODE_ENV: process.env.NODE_ENV
  });
}

const Repository = RepositoryFactory.get(process.env.MIX_APP_REPOSITORY || 'api')

export default {
  init: async (context, payload = {}) => {
    try {
      const response = await repositoryAccess('init', context, payload)
      context.commit("init", response.data);
      context.dispatch("current/init");
    } catch (error) {
      console.error('Failed to initialize application:', error);
      // Set default values to prevent null reference errors
      context.commit("init", {
        user: null,
        sample: null,
        histories: []
      });
      context.dispatch("current/init");
      throw error; // Re-throw to allow component to handle
    }
  },
  copyInputSheet: (context, payload = {}) => {
    context.dispatch("current/copy", payload)
    context.commit('ui/activeIndex', 'input')
  },
  editInputSheet: (context, payload = {}) => {
    context.dispatch("current/edit", payload)
    context.commit('ui/activeIndex', 'edit')
  },
  storeInputSheet: async (context) => {
    const payload = context.getters['current/postParam']
    await repositoryAccess('storeSheet', context, payload)
    .then(res => {
      context.commit('pushInputSheet', res.data.userInputSheet)
      context.dispatch('current/init')
      context.commit('ui/setSelectedGroup', res.data.userInputSheet.groupingName)
      context.commit('ui/activeIndex', 'history')
    })
  },
  updateInputSheet: async (context) => {
    const payload = {
      id: context.state.current.id,
      postParam: context.getters['current/postParam'],
    }
    await repositoryAccess('updateSheet', context, payload)
    .then(res => {
      context.commit('updateInputSheet', res.data.userInputSheet)
      context.dispatch('current/init')
      context.commit('ui/setSelectedGroup', res.data.userInputSheet.groupingName)
      context.commit('ui/activeIndex', 'history')
    })
  },
  deleteInputSheet: async (context, payload = {}) => {
    await repositoryAccess('deleteSheet', context, payload)
    .then(() => {
      context.commit("deleteInputSheet", payload);
    })
  },
  initManage: async (context, payload = {}) => {
    context.commit('ui/activeIndex', 'history')
    const response = await repositoryAccess('initManage', context, payload)
    context.commit("init", response.data);
  },
  showManage: (context, payload = {}) => {
    context.dispatch("current/edit", payload)
    context.commit('ui/activeIndex', 'sheet')
  },
  updateToken: async (context, payload = {}) => {
    await repositoryAccess('updateToken', context, payload)
  }
}

export const repositoryAccess = (repositoryFunction, context, payload) => {
  return new Promise((resolve, reject) => {
    const config = {
      headers: {
        'X-CSRF-TOKEN': context.state.csrfToken
      }
    };
    Repository[repositoryFunction](payload, config)
      .then(response => {
        if (process.env.NODE_ENV !== 'production') {
          console.debug('API Success:', repositoryFunction, response);
        }
        resolve(response)
      })
      .catch(error => {
        console.error('API Error:', repositoryFunction, error.response || error)
        // Provide more detailed error information
        const errorMessage = error.response?.data?.message || error.message || 'Unknown API error';
        const errorStatus = error.response?.status || 'Unknown status';
        reject(new Error(`${repositoryFunction} failed: ${errorStatus} - ${errorMessage}`))
      })
  })
}
