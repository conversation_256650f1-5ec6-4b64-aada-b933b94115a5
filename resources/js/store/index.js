import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import actions from './actions'
import mutations from './mutations'
import ui from './modules/ui'
import current from './modules/current'

Vue.use(Vuex)

const state = {
  csrfToken: null,
  user: null,
  sample: null,
  histories: null,
}

export default new Vuex.Store({
  strict: process.env.NODE_ENV !== 'production',
  state,
  getters,
  actions,
  mutations,
  modules: {
    ui,
    current,
  }
})

export function setId(data) {
  let start = setAccountId(data.credit, 1);
  setAccountId(data.debit, start);
  return data;
}
function setAccountId(data, start) {
  let index = start;
  data.map(d => d.children.map(m => m.children.map(a => {
    if (!a.id) {
      a.id = new Date().getTime().toString(16) + index++;
    }
  })));
  return index;
}
