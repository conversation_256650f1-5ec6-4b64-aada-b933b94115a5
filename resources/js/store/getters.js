export default {
  groupingData: state => state.histories.reduce((acc, cur) => {
    const key = cur.groupingName || '未設定';
    if (!acc.has(key)) acc.set(key, [])
    acc.get(key).push(cur)
    return acc
  }, new Map()),
  // groupCanAdd: (state, getters) =>
  //   (value) => getters.groupingData.size < 30
  //     || getters.groupingData.has(value || '未設定'),
  sheetCanAdd: state => state.user.max_input_sheets === 0 || state.histories.length < state.user.max_input_sheets,
}
