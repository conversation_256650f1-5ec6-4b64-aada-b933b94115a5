
import moment from 'moment'
import * as d3 from "d3";
import { setId } from '../../store'

const state = {
  id: 0,
  key: 0,
  groupingName: '',
  dataDate: null,
  dataName: '',
  capitalizeDebtFromPresident: false,
  accountData: null,
  isDirty: false,
}

export default {
  namespaced: true,
  state,
  actions: {
    init: ({ commit, rootState }) => commit('_init', rootState.sample),
    copy: ({ commit, rootState }, payload) => {
      const data = rootState.histories.find(d => d.id === payload.id)
      commit('_copy', data)
    },
    edit: ({ commit, rootState }, payload) => {
      const data = rootState.histories.find(d => d.id === payload.id)
      commit('_edit', data)
    },
  },
  mutations: {
    set: (state, payload) => state = payload,
    _init: (state, data) => {
      state.id = 0
      state.key = state.id || new Date().getTime()
      state.groupingName = ''
      state.dataDate = moment().format('YYYY-MM-DD')
      state.dataName = ''
      state.capitalizeDebtFromPresident = false
      state.accountData = JSON.parse(JSON.stringify(data))
      state.isDirty = false
    },
    _copy: (state, data) => {
      state.id = 0
      state.key = state.id || new Date().getTime()
      state.groupingName = data.groupingName
      state.dataDate = moment().format('YYYY-MM-DD')
      state.dataName = data.dataName
      state.capitalizeDebtFromPresident = data.capitalizeDebtFromPresident
      state.accountData = setId(JSON.parse(JSON.stringify(data.accountData)))
      state.isDirty = false
    },
    _edit: (state, data) => {
      state.id = data.id
      state.key = state.id
      state.groupingName = data.groupingName
      state.dataDate = data.dataDate
      state.dataName = data.dataName
      state.capitalizeDebtFromPresident = data.capitalizeDebtFromPresident
      state.accountData = setId(JSON.parse(JSON.stringify(data.accountData)))
      state.isDirty = false
    },
    updateProp: (state, payload) => state[payload.propName] = payload.value,
    setDirty: state => state.isDirty = true,
    clearAmounts: state => {
      const clear = data => data.forEach(broad => {
        broad.children.forEach(middle => {
          middle.children.forEach(account => {
            account.value = 0;
          });
        });
      });
      clear(state.accountData.credit)
      clear(state.accountData.debit)
      state.accountData.creditTotal = 0
      state.accountData.debitTotal = 0
    },
    updateAcount: (state, payload) => {
      const account = findAccount(state, a => a.id === payload.id)
      account.name = payload.value
      if (payload.value === 'その他') {
        const children = findMiddle(state, m => m.name === payload.parentName).children
        children.splice(
          children.findIndex(e => e.id === account.id),
          1
        )
        children.push(account)
      }
      state.isDirty = true
    },
    updateAmount: (state, payload) => {
      const account = findAccount(state, a => a.id === payload.id)
      account.value = payload.value
      if (account.type === 'src') {
        const dest = findAccount(state, a => a.type === 'dest' && a.name === account.name)
        dest.value = payload.value
      }
      calcTotal(state)
      state.isDirty = true
    },
    addRow: (state, payload) => {
      const children = findMiddle(state, m => m.name === payload.parentName).children
      const data = { id: new Date().getTime().toString(16), name: payload.name, value: payload.value }
      const index = children.findIndex(e => e.name === 'その他')
      if (payload.name !== 'その他' && index > 0) {
        children.splice(index, 0, data)
      } else {
        children.push(data)
      }
      calcTotal(state)
      state.isDirty = true
    },
    deleteRow: (state, payload) => {
      filterAccount(state, a => a.id !== payload.id)
      calcTotal(state)
      state.isDirty = true
    },
  },
  getters: {
    totalRows: state => rowNum(state.accountData.credit) + rowNum(state.accountData.debit),
    canAddRow: (state, getters) => getters.totalRows < 200,

    filteredAccount: state => {
      const debit = JSON.parse(JSON.stringify(state.accountData.debit))
      // 科目名が「役員借入金」の科目をグラフ化時にエネルギー資本（純資産）として表示する
      capitalizeDebtFromPresident(state.capitalizeDebtFromPresident, debit)
      return {
        credit: filterData(JSON.parse(JSON.stringify(state.accountData.credit)), state.accountData.creditTotal),
        debit: filterData(debit, state.accountData.debitTotal)
      }
    },
    // constitution: (state, getters) => {
    //   if (getters.disableChart) return {}
    //   return calcConstruction(state.accountData, {capitalizeDebtFromPresident: state.capitalizeDebtFromPresident})
    // },
    constitution: (state, getters) => {
      if (getters.disableChart) return {}
      const result = {
        credit: [],
        debit: [],
      };
      // let debit = JSON.parse(JSON.stringify(state.accountData.debit))
      // 科目名が「役員借入金」の科目をグラフ化時にエネルギー資本（純資産）として表示する
      // capitalizeDebtFromPresident(state, debit)
      addConstruction(result.credit, getters.filteredAccount.credit, "悪玉資産", 1);
      addConstruction(result.credit, getters.filteredAccount.credit, "現金等", 0);
      addConstruction(result.credit, getters.filteredAccount.credit, "脂肪資産", 2);
      addConstruction(result.credit, getters.filteredAccount.credit, "利益剰余金", 3);
      addConstruction(result.credit, getters.filteredAccount.credit, "その他包括利益累計額", 3);
      addConstruction(result.debit, getters.filteredAccount.debit, "善玉負債", 1);
      addConstruction(result.debit, getters.filteredAccount.debit, "有利子負債", 0);
      addConstruction(result.debit, getters.filteredAccount.debit, "Ｅ資本", 2);
      return result;
    },
    isValidChart: state => {
      // -2%以下の科目がある場合はグラフ表示できない
      return isValidData(state.accountData.credit, state.accountData.creditTotal) &&
        isValidData(state.accountData.debit, state.accountData.debitTotal);
    },
    // isValidLimit: state => {
    //   // 借方・貸方にそれぞれ2%以上の項目があること
    //   const valid = (arg, total) => {
    //     if (total === 0) {
    //       return true
    //     }
    //     const limit = total * 0.0195;
    //     return !arg.every(d => d.children.every(m => m.children.every(a => a.value <= limit)))
    //   }
    //   return valid(state.accountData.credit, state.accountData.creditTotal) &&
    //    valid(state.accountData.debit, state.accountData.debitTotal)
    // },
    isBalance: state => {
      return state.accountData.creditTotal === state.accountData.debitTotal
    },
    disableChart: (state, getters) => {
      return !getters.isBalance || state.accountData.creditTotal === 0 ||
        !getters.isValidChart // || !getters.isValidLimit
    },
    postParam: (state, getters) => {
      return {
        groupingName: state.groupingName,
        dataDate: state.dataDate,
        dataName: state.dataName,
        capitalizeDebtFromPresident: state.capitalizeDebtFromPresident,
        accountJson: JSON.stringify(state.accountData),
        filteredAccountJson: JSON.stringify(getters.filteredAccount),
        constitutionJson: JSON.stringify(getters.constitution),
        disableChart: getters.disableChart,
      }
    },
  },
}

function calcTotal(state) {
  const calc = (data) => d3.hierarchy({ children: JSON.parse(JSON.stringify(data)) }).sum(d => d.value).value;
  state.accountData.creditTotal = calc(state.accountData.credit)
  state.accountData.debitTotal = calc(state.accountData.debit)
}

function findAccount(state, find) {
  for (const d of state.accountData.credit) {
    for (const m of d.children) {
      for (const a of m.children) {
        if (find(a)) {
          return a;
        }
      }
    }
  }
  for (const dd of state.accountData.debit) {
    for (const dm of dd.children) {
      for (const da of dm.children) {
        if (find(da)) {
          return da;
        }
      }
    }
  }
}

function findMiddle(state, find) {
  for (const d of state.accountData.credit) {
    for (const m of d.children) {
      if (find(m)) {
        return m;
      }
    }
  }
  for (const dd of state.accountData.debit) {
    for (const dm of dd.children) {
      if (find(dm)) {
        return dm;
      }
    }
  }
}

function filterAccount(state, func) {
  state.accountData.credit.forEach(
    d => d.children.forEach(
      m => m.children = m.children.filter(a => func(a))
    )
  )
  state.accountData.debit.forEach(
    d => d.children.forEach(
      m => m.children = m.children.filter(a => func(a))
    )
  )
}

/** 科目名が「役員借入金」の科目をグラフ化時にエネルギー資本（純資産）として表示する */
function capitalizeDebtFromPresident(capitalizeDebtFromPresident, debit) {
  if (capitalizeDebtFromPresident) {
    debit.map(d => {
      if (d.name !== '純資産') {
        d.children.map(m => {
          if (m.constitutionType !== 'Ｅ資本') {
            const index = m.children.findIndex(a => a.name === '役員借入金');
            if (index >= 0) {
              let account = m.children.splice(index, 1);
              debit[2].children.splice(-1, 0, {
                children: account,
                color: 2,
                constitutionType: 'Ｅ資本',
                name: m.name
              });
            }
          }
        });
      }
    });
  }
  return debit
}

function rowNum(arg) {
  return arg.reduce((acc, d) => acc + d.children.reduce((acc2, m) => acc2 + m.children.length, 0), 0);
}

function filterData(arg, total) {
  const limit = total * 0.0195;
  const filterValueData = arg.map(d => {
    d.children.map(m => {
      if (m.children.length > 1) {
        // 2%未満の科目の値を合算
        const etcValue = d3.sum(m.children.filter(a => a.value <= limit), a => a.value)
        // その他に合算する
        const etc = m.children.find(a => a.name === 'その他')
        // 2%未満の科目をその他にまとめるので省く
        m.children = m.children.filter(a => a.value > limit);
        if (etc && etc.value > limit) {
          etc.value += etcValue
        } else {
          m.children.push({ name: 'その他', value: etcValue })
        }
        // 2%未満のその他が省かれないようにする
        // m.children = m.children.filter(a => a.value > limit);
      }
    });
    d.children.map(m => {
      m.children.map(a => a.percent = Math.round(a.value / total * 1000) / 1000) // 表示用割合
      m.percent = d3.sum(m.children, a => a.percent) // 表示用割合
    });
    d.percent = d3.sum(d.children, m => m.percent) // 表示用割合
    // グラフのほうで省くのでここでは中分類を省かない
    // d.children = d.children.filter(df => df.children.length > 0);
    return d;
  });
  // グラフのほうで省くのでここでは大分類を省かない
  // return filterValueData.filter(d => d.children.length > 0);
  return filterValueData;
}

function addConstruction(result, data, type, color) {
  const value = d3.sum(data, d => d3.sum(d.children, c => c.constitutionType === type ? d3.sum(c.children, cc => cc.value) : 0));
  if (value > 0) {
    // 表示用割合
    const percent = d3.sum(data, d => d3.sum(d.children, c => c.constitutionType === type ? c.percent : 0));
    result.push({ name: type, color, value, percent })
  }
}

function isValidData(arg, total) {
  // 0未満の科目がある場合はグラフ表示できない
  if (total === 0) {
    return true;
  }
  return arg.every(d => d.children.every(m => m.children.every(a => a.value >= 0)))
}
