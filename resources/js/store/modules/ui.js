
const state = {
  activeIndex: 'input',
  selectedGroup: null,
  unit: 0, // 単位index指定
};

const units = [
  { label: '円', value: 1 },
  { label: '千円', value: 1000 },
  { label: '百万円', value: 1000000 },
]

export default {
  namespaced: true,
  state,
  getters: {
    selectedGroupIndex: (state, getters, rootState, rootGetters) => {
      return String(Array.from(rootGetters.groupingData.keys()).indexOf(state.selectedGroup))
    },
    units: () => units
  },
  mutations: {
    activeIndex: (state, payload) => state.activeIndex = payload,
    setSelectedGroup: (state, payload) => state.selectedGroup = payload || '未設定',
    updateUnit: (state, payload) => state.unit = payload,
  }
}
