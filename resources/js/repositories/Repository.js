import axios from "axios"

const baseDomain = ""
const baseURL = `${baseDomain}/`

// Debug repository configuration (development only)
if (process.env.NODE_ENV !== 'production') {
  console.debug('Repository configuration:', {
    baseDomain,
    baseURL,
    currentLocation: window.location.href
  });
}

const instance = axios.create({
  baseURL
});

// Add request interceptor for debugging
instance.interceptors.request.use(
  config => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug('API Request:', config.method?.toUpperCase(), config.url, config);
    }
    return config;
  },
  error => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
instance.interceptors.response.use(
  response => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug('API Response:', response.status, response.config.url, response.data);
    }
    return response;
  },
  error => {
    console.error('API Response Error:', error.response?.status, error.config?.url, error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export default instance;
