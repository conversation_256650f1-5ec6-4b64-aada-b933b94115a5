import Repository from "./Repository"

export default {
  init: (payload, config) => Repository.get('init', config),
  initManage: (payload, config) => Repository.get(`manage/history/${payload.userId}/init`, config),
  storeSheet: (payload, config) => Repository.post('sheets', payload, config),
  updateSheet: (payload, config) => Repository.put(`sheets/${payload.id}`, payload.postParam, config),
  deleteSheet: (payload, config) => Repository.delete(`sheets/${payload.id}`, config),
  updateToken:  (payload, config) => Repository.get('sheets', payload, config),
}
