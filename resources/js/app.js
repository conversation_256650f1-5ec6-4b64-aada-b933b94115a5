import Vue from "vue"
import store from './store'
import lang from 'element-ui/lib/locale/lang/ja'
import locale from 'element-ui/lib/locale'
import '../sass/element-variables.scss'

locale.use(lang)

Vue.component("App", require("./components/app.vue").default);
Vue.directive('visible', function(el, binding) {
	el.style.visibility = binding.value ? 'visible' : 'hidden';
});
new Vue({
  store,
  el: "#app"
});
