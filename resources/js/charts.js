import * as d3 from "d3";

const numberFormat = d3.format(',.0f');
// パーセンテージは小数点第１位まで表示する
const formatPercent = d3.format('.1%');
// A4縦に2%の項目に科目名が表示できる大きさ（2%の高さがfontSize*2になるように）
const fontSize = 12;

/**
 * 推移図作成
 * @param {*} transition
 * @param {Object} user 色のユーザー設定
 * @param {*} onClick
 * @param {Number} unit 金額の表示単位
 */
export function transition(transition, user, onClick, unit) {
  if (transition.length < 1) {
    return;
  }
  // console.debug(transition)
  // パスの描画のために最低1以上のマージンが必要
  const margin = { top: 10, right: 7, bottom: 40, left: 7 };
  const height = fontSize * 100; // 2%の高さがfontSize*2になるようにする
  const colWidth = 100;
  // web用の幅（スクロールする）
  const width = transition.length * colWidth;
  // PDF用の幅等（A4横、一定数で改ページできるようにsvgを分割する）
  const pdfPageDataLength = 20;
  const pdfWidth = pdfPageDataLength * colWidth;
  const pdfPageNum = Math.ceil(transition.length / pdfPageDataLength);

  // 金額を指定の単位で表示する TELEGRAM-20 千円未満の金額があった場合は１千円で表記する
  const formatAmount = (v) => numberFormat(Math.max(v / unit, 1));

  const color = d3
    .scaleOrdinal()
    .domain([0, 1, 2, 3])
    .range([user.theme.color_1, user.theme.color_2, user.theme.color_3, user.theme.color_4]);

  // web用横軸
  const x = d3
    .scaleBand()
    .domain(transition.map((d, i) => i))
    .range([0, width])
    .padding(0.08);
  const xAxis = g => g
    .attr("transform", `translate(${margin.left},${height + margin.top})`)
    .style("font-size", fontSize)
    .call(d3.axisBottom(x).tickFormat(d => transition[d].dataName).tickSizeOuter(0));

  const y = d3.scaleLinear()
    // stackOffsetExpand指定しているのでdomainは0-1
    .range([height, 0]);

  // PDF用横軸
  const pdfX = Array(pdfPageNum);
  for (var i = 0; i < pdfPageNum; i++) {
    pdfX[i] = d3
      .scaleBand()
      .domain([...Array(pdfPageDataLength)].map((d, idx) => idx + i * pdfPageNum))
      .range([0, pdfWidth])
      .padding(0.08);
  }
  const pdfXAxis = (g, idx) => g
    .attr("transform", `translate(${margin.left},${height + margin.top})`)
    .style("font-size", fontSize)
    .call(d3.axisBottom(pdfX[idx]).tickFormat(d => transition[d] ? transition[d].dataName : "").tickSizeOuter(0));

  // 推移A（借方体質）
  {
    const transitions = transition.map(d => ({
      id: d.id, // クリック時に表示する体質図を判定するために使用する
      dataName: d.dataName,
      values: d.constitutionData.credit,
      total: d.accountData.creditTotal
    }))
    const keys = ["その他包括利益累計額", "利益剰余金", "脂肪資産", "現金等", "悪玉資産"];
    chart('#ac', transitions, keys);
    forPdf('#acpdf', transitions, keys);
  }
  // 推移A'（貸方体質）
  {
    const transitions = transition.map(d => ({
      id: d.id,
      dataName: d.dataName,
      values: d.constitutionData.debit,
      total: d.accountData.debitTotal
    }))
    const keys = ["Ｅ資本", "有利子負債", "善玉負債"]
    chart('#ad', transitions, keys);
    forPdf('#adpdf', transitions, keys);
  }
  // 推移B（借方小分類）
  const credit = transition.map(d => {
    const h = d3.hierarchy({ children: d.filteredAccountData.credit });
    return {
      id: d.id,
      dataName: d.dataName,
      values: h.leaves().map( (d , i) => {
        return {
          //体質図と推移図で項目の並び順を同じにするためにキーはインデックスにする
          key: i,
          color: d.data.color || d.parent.data.color,
          ...d.data
        };
      }),
      total: d.accountData.creditTotal
    };
  })
  // const creditKeys = getKeys(transition.map(d => d.filteredAccountData.credit));
  const creditKeys = d3.range(d3.max(credit, (d) => d.values.length)).reverse();
  chart('#bc', credit, creditKeys);
  forPdf('#bcpdf', credit, creditKeys);
  // 推移B'（貸方小分類）
  const debit = transition.map(d => {
    const h = d3.hierarchy({ children: d.filteredAccountData.debit });
    return {
      id: d.id,
      dataName: d.dataName,
      values: h.leaves().map((d,i) => {
        return {
          //体質図と推移図で項目の並び順を同じにするためにキーはインデックスにする
          key: i,
          color: d.data.color || d.parent.data.color,
          ...d.data
        };
      }),
      total: d.accountData.debitTotal
    };
  })
  //const debitKeys = getKeys(transition.map(d => d.filteredAccountData.debit));
  //体質図と推移図で項目の並び順を同じにするためにキーは要素のなかで一番項目数が多いもののインデックス配列にする
  const debitKeys = d3.range(d3.max(debit, (d) => d.values.length)).reverse();

  console.log("debit", debit);
  console.log("debitKeys", debitKeys);

  chart('#bd', debit, debitKeys);
  forPdf('#bdpdf', debit, debitKeys);

  //MEMO: 体質図と推移図で項目の並び順を同じにするために使用しなくなった
  function getKeys(transitions) {
    const bMap = new Map();
    transitions
      .forEach(d => {
        d.forEach(b => {
          if (!bMap.has(b.name)) {
            bMap.set(b.name, new Map());
          }
          const mMap = bMap.get(b.name)
          b.children.forEach(m => {
            if (!mMap.has(m.name)) {
              mMap.set(m.name, new Set());
            }
            const aSet = mMap.get(m.name);
            // 別の分類と科目名がかぶる可能性もあるので、大分類名+中分類名+科目名をキーとして使用する
            m.children.forEach(a => {
              // その他は最後にするため、ここでは追加しない
              if (a.name !== 'その他') aSet.add(b.name + m.name + a.name)
            })
          })
          // 役員借入金対応
          if (b.name === '純資産' && mMap.size > 1) {
            const m = mMap.get('純資産')
            mMap.delete('純資産')
            mMap.set('純資産', m)
          }
        })
      });
    console.debug(bMap);
    let result = [];
    bMap.forEach((m, bkey) => m.forEach((a, mkey) => {
      // その他を最後に追加する
      result = new Array(...a, bkey + mkey + 'その他').reverse().concat(result)
    }))
    // console.debug(result);
    return result;
  }



  /**
   * Web用d3.stackチャートを描画する
   * @param {String} id SVGを描画する対象のIDセレクタのID値
   * @param {Array} data
   * @param {Array} keys グラフに表示する項目
   */
  function chart(id, data, keys) {
    d3.select(id).select("svg").remove();
    const svg = d3
      .select(id)
      .append("svg")
      .attr("width", width + margin.right + margin.left)
      .attr("height", height + margin.top + margin.bottom);

    const stack = d3
      .stack()
      // ゼロのベースラインを適用し、トップラインが常に1になるように各ポイントの値を正規化します。
      .offset(d3.stackOffsetExpand)
      .keys(keys)
      .value((d, key) => {
        // キーに対する値
        const data = d.values.find(e => e.key === key || e.name === key)
        return data ? data.value : 0
      });

    const series = stack(data);

    series.map(
      d => (d.forEach(v => {
            v.orgData = v.data.values.find(e => e.key === d.key || e.name === d.key);
            v.y = y(v[1]);
            v.height = y(1 - v[1] + v[0]);
        }), d)
    );

    const seriesGroup = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`)
      .selectAll("g")
      .data(series)
      .enter()
      .append("g");
    const cell = seriesGroup
      .selectAll("g")
      .data(d => d)
      .join("g")
      .on("click", onClick)
      .attr("transform", (d, i) => `translate(${x(i)}, ${d.y})`)
      .filter(d => d.height > 0);

    cell
      .each(d => {
        d.color = d.orgData.color;
        d.accountName = d.orgData.name;
        d.percent = d.orgData.percent;
        d.amount = d.orgData.value;
        d.cellHeight = d.height;
        d.cellWidth = x.bandwidth();
        d.titleText = `${d.data.dataName}\n${d.accountName}\n${formatAmount(d.amount)}\n${formatPercent(d.percent)}`
      });
    writeCell(cell, color, formatAmount);

    // TELEGRAM-39 【キャッシュ体質図】推移表の横の汎用メモリを消してほしい
    // const yAxis = g => g
    //   .attr("transform", `translate(${margin.left},${margin.top})`)
    //   .call(d3.axisLeft(y).ticks(5, "%"))
    // // .call(g => g.selectAll(".domain").remove());
    // svg.append("g").call(yAxis);

    svg.append("g").call(xAxis);
    svg.selectAll(".tick").selectAll("text").attr('y', d => d % 2 === 0 ? 9 : 13 + fontSize)
  }

  /**
   * PDF用d3.stackチャートを描画する
   * @param {String} id SVGを描画する対象のIDセレクタのID値
   * @param {Array} data
   * @param {Array} keys グラフに表示する項目
   */
  function forPdf(id, data, keys) {
    // console.debug(data)
    d3.select(id).selectAll("svg").remove();

    for (var i = 0; i < pdfPageNum; i++) {
      const stack = d3
        .stack()
        .offset(d3.stackOffsetExpand)
        .keys(keys)
        .value((d, key) => {
          // キーに対する値
          const data = d.values.find(e => e.key === key || e.name === key)
          return data ? data.value : 0
        });

      const series = stack(data.slice(i * pdfPageDataLength, (i + 1) * pdfPageDataLength));
      // console.debug(series)

      series.map(d => (d.forEach(v => {
        v.orgData = v.data.values.find(e => e.key === d.key || e.name === d.key);
        v.y = y(v[1]);
        v.height = y(1 - v[1] + v[0]);
      }), d));
      // console.debug(series)

      const svg = d3.select(id).append("svg")
        .attr("width", pdfWidth + margin.right + margin.left)
        .attr("height", height + margin.top + margin.bottom);
      const seriesGroup = svg
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`)
        .selectAll("g")
        .data(series)
        .enter()
        .append("g");
      const cell = seriesGroup
        .selectAll("g")
        .data(d => d)
        .join("g")
        .attr("transform", (d, i) => `translate(${x(i)}, ${d.y})`)
        .filter(d => d.height > 0);

      cell
        .each(d => {
          d.color = d.orgData.color;
          d.accountName = d.orgData.name;
          d.percent = d.orgData.percent;
          d.amount = d.orgData.value;
          d.cellHeight = d.height;
          d.cellWidth = pdfX[i].bandwidth();
          d.titleText = `${d.data.dataName}\n${d.accountName}\n${formatAmount(d.amount)}\n${formatPercent(d.percent)}`
        });
      writeCell(cell, color, formatAmount);

      svg.append("g").call(pdfXAxis, i);
      svg.selectAll(".tick").selectAll("text").attr('y', d => d % 2 === 0 ? 9 : 13 + fontSize)
    }
  }
}

/**
 * 体質図作成
 * @param {*} filterdAccount
 * @param {*} constitution
 * @param {Object} user 色のユーザー設定
 */
export function constitution(filterdAccount, constitution, user, unit) {
  const headerFontSize = 14;
  const headerLineHight = 1.3;
  const margin = { top: headerFontSize * headerLineHight, right: 1, bottom: 1, left: 1 };
  const height = fontSize * 100;
  const colWidth = 80;
  const addColWidth = 40;
  const width = colWidth * 8 + addColWidth * 2;

  // 金額を指定の単位で表示する TELEGRAM-20 千円未満の金額があった場合は１千円で表記する
  const formatAmount = (v) => numberFormat(Math.max(v / unit, 1));

  const color = d3
    .scaleOrdinal()
    .domain([0, 1, 2, 3])
    .range([user.theme.color_1, user.theme.color_2, user.theme.color_3, user.theme.color_4]);

  d3.select("#constitution").select("svg").remove();
  const svg = d3
    .select("#constitution")
    .append("svg")
    .attr("width", width + margin.right + margin.left)
    .attr("height", height + margin.top + margin.bottom);

  const haeder = svg.append("g")
    .attr("transform", `translate(${margin.left},0)`)
    .attr("width", colWidth)
    .attr("height", margin.top);
  haeder.append('text')
    .attr("font-size", headerFontSize)
    .attr("text-anchor", "middle")
    .attr("x", colWidth * 1.5)
    .attr("y", headerFontSize)
    .text("A");
  haeder.append('text')
    .attr("font-size", headerFontSize)
    .attr("text-anchor", "middle")
    .attr("x", colWidth * 3 + (colWidth + addColWidth) * 0.5)
    .attr("y", headerFontSize)
    .text("B");
  haeder.append('text')
    .attr("font-size", headerFontSize)
    .attr("text-anchor", "middle")
    .attr("x", colWidth * 3 + (colWidth + addColWidth) * 1.5)
    .attr("y", headerFontSize)
    .text("B'");
    haeder.append('text')
    .attr("font-size", headerFontSize)
    .attr("text-anchor", "middle")
    .attr("x", colWidth * 4.5 + (colWidth + addColWidth) * 2)
    .attr("y", headerFontSize)
    .text("A'");

  // 借方 大分類・中分類・小分類(B)
  draw(filterdAccount.credit, 0)
  // 貸方 大分類・中分類・小分類(B')
  draw(filterdAccount.debit, width / 2, true)
  // 借方 体質(A)
  draw(constitution.credit, colWidth)
  // 貸方 体質(A')
  draw(constitution.debit, width - colWidth * 2)

  function draw(data, offset, flip = false) {
    const root = d3
      .partition()
      .size([height, width])
      .padding(0)(d3.hierarchy({ children: data }).sum(d => d.value))
    const cell = svg
      .append("g")
      .attr("transform", `translate(${margin.left + offset},${margin.top})`)
      .selectAll("g")
      .data(root.descendants().filter(d => d.depth > 0))
      .join("g")
      .each(d => {
        d.cellHeight = d.x1 - d.x0;
        d.cellWidth = d.depth === 3 ? addColWidth + colWidth : colWidth;
        d.translate = flip ? d.depth === 3 ? 0: d.depth === 2 ? addColWidth + colWidth : addColWidth + colWidth * 3 : d.depth === 1 ? 0 : colWidth * d.depth
        d.color = d.data.color || d.parent.data.color;
        d.accountName = d.data.name;
        d.percent = d.data.percent;
        d.amount = d.value;
        d.titleText = `${d.accountName}\n${formatAmount(d.amount)}\n${formatPercent(d.percent)}`;
      })
      .attr("transform", d => `translate(${d.translate},${d.x0})`)
    writeCell(cell.filter(d => d.cellHeight > 0), color, formatAmount);
  }
}

function writeCell(cell, color, formatAmount) {
  cell
    .classed('color3', d => d.color === 3)
    .append("rect")
    .style("stroke", "black")
    .style("stroke-width", 0.5)
    .style("fill", d => color(d.color))
    .attr("width", d => d.cellWidth)
    .attr("height", d => d.cellHeight);

  // 科目名が枠に収まる（体質図BB'につては8文字以内）
  cell
    .filter(d => d.cellHeight > fontSize * 1.5)
    .filter(d => d.depth === 3 ?
      d.accountName.length <= 8 :
      d.cellWidth > fontSize * d.accountName.length)
    .append("text")
    .attr("font-size", fontSize)
    .attr("text-anchor", "middle")
    .attr("x", d => d.cellWidth / 2)
    .attr("y", d => cellY(d))
    .text(d => d.accountName);

  // 科目名が長い
  const text = cell
    .filter(d => d.cellHeight > fontSize * 1.5)
    .filter(d => d.depth === 3 ?
      d.accountName.length > 8 :
      d.cellWidth <= fontSize * d.accountName.length)
    .each(d => d.maxTextLength = d.depth === 3 ? 8 :
      d.accountName.length > 12 ? 8 : Math.floor(d.cellWidth / fontSize) - 1)
    .append("text")
    // 体質図BB'以外は12文字以上の場合はフォントサイズを小さくして横8文字収まるようにする
    .attr("font-size", d => d.depth !== 3 && d.accountName.length > 12 ? fontSize - 2 : fontSize)
    .attr("text-anchor", "middle")
    .attr("x", d => d.cellWidth / 2)
    .attr("y", d => d.cellHeight < fontSize * 5 ? cellY(d) : cellY(d) - fontSize * 1.4)
    .text(d => d.accountName.substring(0, d.maxTextLength));
  text
    .filter(d => d.cellHeight >= fontSize * 5)
    .append("tspan")
    .attr("x", d => d.cellWidth / 2)
    .attr("y", d => cellY(d) - fontSize * 0.2)
    .text(d => d.accountName.substring(d.maxTextLength, d.maxTextLength * 2));

  cell
    .filter(d => d.cellHeight > fontSize * 3)
    .append("text")
    .attr("font-size", fontSize)
    .attr("text-anchor", "middle")
    .attr("x", d => d.cellWidth / 2)
    .attr("y", d => d.cellHeight - fontSize / 2)
    .text(d => formatPercent(d.percent));

  cell
    .filter(d => d.cellHeight > fontSize * 7)
    .append('text')
    .attr("font-size", fontSize * 0.8)
    .attr("text-anchor", "middle")
    .attr("x", d => d.cellWidth / 2)
    .attr("y", d => d.cellHeight - fontSize * 2)
    .text(d => formatAmount(d.amount));

  cell.append("title").text(d => d.titleText);
}

function cellY(d) {
  const center = (d.cellHeight) / 2 + fontSize / 2;
  if (d.cellHeight <= fontSize * 3) {
    return center;
  } else if (d.cellHeight < fontSize * 5) {
    return d.cellHeight / 2;
  }
  return center;
}
