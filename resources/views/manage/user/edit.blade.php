@extends('layouts.manage')

@section('content')
ユーザー詳細画面
<ul>
    @foreach ($errors->all() as $error)
        <li>{{ $error }}</li>
    @endforeach
</ul>
<form name="userform" action="{{ route('manage.entry') }}" method="POST">
    <input id="user_id" type="hidden" name="user_id" value="{{$item['id']}}">
    @csrf
    @include('layouts.userform')
    <a href="">キャッシュ体質図履歴</a><br/>
    <button type="submit" >決定
    </button>
</form>
<hr>
<form id="logout-form" action="{{ route('manage.logout') }}" method="POST">
    @csrf
    <button type="submit" >ログアウト
    </button>
</form>
@endsection
