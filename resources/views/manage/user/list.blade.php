@extends('layouts.manage')

@section('content')
ユーザー一覧
<form id="create" action="{{ route('manage.create') }}" method="GET">
    <button type="submit" >新規作成</button>
    <input id="search" type="text" name="search" placeholder="検索" onInput="inclemental(this)" autofocus>
</form>
<table style='border: 1px solid;'>
    <thead>
        <tr>
            <th>企業名</th>
            <th>ユーザー名</th>
            <th>アカウントID</th>
            <th>上場情報リンク</th>
            <th>開始日/期限日</th>
            <th>最終更新日</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach ($users as $user)
        <tr id="user_{{$user->id}}" @if ($user->end_at < now()) style="background-color:darkgray" @endif>
            <td><a href="/manage/user/edit/{{$user->id}}">{{$user->company_name}}</a></td>
            <td>{{$user->user_name}}</td>
            <td>{{$user->account_id}}</td>
            <td>@if ($user->show_link) ON @else OFF @endif</td>
            <td>{{$user->start_at->format('Y-m-d')}} ~ {{$user->end_at->format('Y-m-d')}}</td>
            <td>{{$user->last_update_at}}</td>
            <td><button type="button" onclick="copyUser({{$user->id}})">情報コピー</button>

<div id="info_{{$user->id}}" style="display:none;">
<p>企業名：{{$user->company_name}}</p>
<p>名前：{{$user->user_name}}</p>
<p>アカウントID：{{$user->account_id}}</p>
<p>パスワード：{{$user->password}}</p>
<p>利用開始日：{{$user->start_at->format('Y-m-d')}}</p>
<p>利用期限日：{{$user->end_at->format('Y-m-d')}}</p>
</div>

            </td>
        </tr>
        @endforeach
    </tbody>
</table>

<form id="logout-form" action="{{ route('manage.logout') }}" method="POST">
    @csrf
    <button type="submit" >ログアウト
    </button>
</form>

<script type="text/javascript">
<!--
function inclemental($this) {
    const search_list = @json($search_list);
    search_list.forEach(word => {
        var idx = word['index'];
        var usertr = document.getElementById('user_'+idx);
        if (word['word'].indexOf($this.value) !== -1) {
            usertr.style.display = "";
        } else {
            usertr.style.display = "none";
        }
    });
}

function copyUser(idx) {
    var userinfo = document.getElementById('info_'+idx);

    alert('クリップボードにコピー(する予定)\n' + userinfo.innerText);
}
-->
</script>
@endsection
