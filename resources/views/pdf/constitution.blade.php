<html>

<head>
    <title>キャッシュ体質図</title>
    <link href="{{ base_path() . '/public/css/app.css' }}" rel="stylesheet">
    {{-- <link href="/css/app.css" rel="stylesheet"> --}}
    <style>
    #constitution {
        width: 720px;
        margin: 0 auto;
    }
    .header {
        width: 100%;
        font-size: 1.2em;
        margin: 0 auto;
    }
    .header span {
        display: inline-block;
    }
    .header span:nth-child(2n+1) {
        width: 36%;
    }
    .header span:nth-child(2) {
        width: 28%;
        text-align: center;
    }
    .header span:nth-child(3) {
        text-align: right;
    }
    .unitDisp {
        font-size: 0.9rem;
        margin-top: 0.4rem;
    }
    .CompanyName {
        width: 100%;
        margin-top: 0.5em;
        font-size: 1.4em;
        text-align: right;
   }

    @font-face{
        font-family: ipag;
        font-style: normal;
        font-weight: normal;
        src:url('{{ resource_path("fonts/ipag.ttf")}}');
    }

    * {
        font-family: 'ipag' !important;
    }
    </style>
</head>

<body>
    {{-- 表示が崩れるので改行しない --}}
    <div class="header"><span>{{ $leftTitle1 }}</span><span>キャッシュ体質図</span><span>{{ $rightTitle }}</span></div>
    <div class="header"><span style="padding-left: 1em;box-sizing: border-box;">{{ $leftTitle2 }}</span><span></span><span class="unitDisp">(単位：{{ $unit }})</span></div>
    <div id="constitution" class="pdfchart">{!! $svg !!}</div>
    <div class="CompanyName">
        <span>{{ $companyName }}</span>
    </div>
</body>

</html>
