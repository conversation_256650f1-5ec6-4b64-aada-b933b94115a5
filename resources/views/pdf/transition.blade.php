<html>

<head>
    <title>キャッシュ体質推移図</title>
    <link href="{{ base_path() . '/public/css/app.css' }}" rel="stylesheet">
    {{-- <link href="/css/app.css" rel="stylesheet"> --}}
    <style>
    .pdfchart {
        /* width: 720px; */
        margin: 0 auto;
        page-break-inside: avoid;
    }
    .header {
        width: 100%;
        font-size: 1.2em;
        margin: 0 auto 0.6em;
    }
    .header span {
        display: inline-block;
    }
    .header span:nth-child(2n+1) {
        width: 33%;
    }
    .header span:nth-child(2) {
        width: 34%;
        text-align: center;
    }
    .header span:nth-child(3) {
        text-align: right;
    }
    .unitDisp {
        width: 100%;
        margin-bottom: 0.4em;
        font-size: 0.9em;
        text-align: right;
    }
    .CompanyName {
        width: 100%;
        margin-top: 0.5em;
        font-size: 1.4em;
        text-align: right;
        page-break-after: always;
   }
   .CompanyName:last-child {
        page-break-after: auto;
    }

    @font-face{
        font-family: ipag;
        font-style: normal;
        font-weight: normal;
        src:url('{{ resource_path("fonts/ipag.ttf")}}');
    }

    * {
        font-family: 'ipag' !important;
    }
    </style>
</head>

<body>
    @foreach ( $data->ac as $svg)
    <div class="header"><span>{{ $leftTitle }}</span><span>キャッシュ体質推移A</span><span id="date">{{ $rightTitle }}</span></div>
    <div class="unitDisp">
        <span>(単位：{{ $unit }})</span>
    </div>
    <div class="pdfchart">{!! $svg !!}</div>
    <div class="CompanyName">
        <span>{{ $companyName }}</span>
    </div>
    @endforeach
    @foreach ( $data->ad as $svg)
    <div class="header"><span>{{ $leftTitle }}</span><span>キャッシュ体質推移A'</span><span id="date">{{ $rightTitle }}</span></div>
    <div class="unitDisp">
        <span>(単位：{{ $unit }})</span>
    </div>
    <div class="pdfchart">{!! $svg !!}</div>
    <div class="CompanyName">
        <span>{{ $companyName }}</span>
    </div>
    @endforeach
    @foreach ( $data->bc as $svg)
    <div class="header"><span>{{ $leftTitle }}</span><span>キャッシュ体質推移B</span><span id="date">{{ $rightTitle }}</span></div>
    <div class="unitDisp">
        <span>(単位：{{ $unit }})</span>
    </div>
    <div class="pdfchart">{!! $svg !!}</div>
    <div class="CompanyName">
        <span>{{ $companyName }}</span>
    </div>
    @endforeach
    @foreach ( $data->bd as $svg)
    <div class="header"><span>{{ $leftTitle }}</span><span>キャッシュ体質推移B'</span><span id="date">{{ $rightTitle }}</span></div>
    <div class="unitDisp">
        <span>(単位：{{ $unit }})</span>
    </div>
    <div class="pdfchart">{!! $svg !!}</div>
    <div class="CompanyName">
        <span>{{ $companyName }}</span>
    </div>
    @endforeach
</body>

</html>
