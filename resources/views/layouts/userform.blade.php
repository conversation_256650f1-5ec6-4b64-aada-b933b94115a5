<label for="company_name">企業名：</label>
<input id="company_name" type="text" name="company_name" value="{{($item['company_name']) ?? ''}}" required autofocus><br/>
<label for="user_name">名前：</label>
<input id="user_name" type="text" name="user_name" value="{{($item['user_name']) ?? ''}}" required><br/>
<label for="account_id">アカウントID：</label>
<input id="account_id" type="text" name="account_id" value="{{($item['account_id']) ?? ''}}" required><br/>
<label for="password">パスワード：</label>
<input id="password" type="text" name="password" value="{{($item['password']) ?? ''}}" required>
<button type="button" onclick="genPassword()">生成</button><br/>
<label for="start_at">利用開始日：</label>
<input id="start_at" type="date" name="start_at" value="{{($item['start_at']) ?? now()->format('Y-m-d')}}" required><br/>
<label for="end_at">利用期限日：</label>
<input id="end_at" type="date" name="end_at" value="{{($item['end_at']) ?? ''}}" required><br/>
<label for="mst_theme_id">グラフカラー：</label>
<input id="mst_theme_id" type="text" name="mst_theme_id" value="{{($item['mst_theme_id']) ?? ''}}" readonly required>
<button type="button" onclick="openDialog()">変更</button><br/>

<label for="show_link">上場情報リンク：</label>
<label for="show_link1">オン</label>
<input id="show_link1" type="radio" name="show_link" value=1 required checked>
<label for="show_link0">オフ</label>
<input id="show_link0" type="radio" name="show_link" value=0 required><br/>

<dialog>
    <table>
        <tbody>
            @foreach ($themes as $theme)
            <tr>
                <td>{{$theme->id}}</td>
                <td style="width:50px; background-color:{{$theme->color_1}}"></td>
                <td style="width:50px; background-color:{{$theme->color_2}}"></td>
                <td style="width:50px; background-color:{{$theme->color_3}}"></td>
                <td style="width:50px; background-color:{{$theme->color_4}}"></td>
                <td><button type="button" onclick="selectTheme({{$theme->id}})">決定</button></td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <button type="button" onclick="closeDialog()">閉じる</button>
</dialog>

<script type="text/javascript">
<!--
function openDialog() {
    const modal = document.querySelector('dialog');
    modal.showModal();
}

function closeDialog() {
    const modal = document.querySelector('dialog');
    modal.close();
}

function selectTheme(id) {
    document.userform.mst_theme_id.value = id;
    const modal = document.querySelector('dialog');
    modal.close();
}

function genPassword(length = 12)
{
    let password_base = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let password = '';
    for (let i = 0; i < length; i++) {
        password += password_base.charAt(Math.floor(Math.random() * password_base.length));
    }
    document.userform.password.value = password;
}
-->
</script>