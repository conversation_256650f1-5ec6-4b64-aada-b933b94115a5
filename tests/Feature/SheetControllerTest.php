<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserInputSheet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SheetControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /**
     * Test authenticated user can store a new sheet
     * 正常系: 認証済みユーザーが新しいシートを作成できることを確認
     */
    public function test_authenticated_user_can_store_new_sheet()
    {
        $user = factory(User::class)->create();

        $sheetData = [
            'groupingName' => 'テスト企業',
            'dataName' => 'テストデータ',
            'dataDate' => '2024-01-01',
            'capitalizeDebtFromPresident' => false,
            'accountJson' => json_encode(['現金' => 1000000]),
            'filteredAccountJson' => json_encode(['現金' => 1000000]),
            'constitutionJson' => json_encode(['assets' => 5000000]),
            'disableChart' => false
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $sheetData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'userInputSheet' => [
                    'userId' => $user->id,
                    'groupingName' => 'テスト企業',
                    'dataName' => 'テストデータ'
                ]
            ]);

        $this->assertDatabaseHas('user_input_sheets', [
            'user_id' => $user->id,
            'grouping_name' => 'テスト企業',
            'data_name' => 'テストデータ'
        ]);

        // ユーザーの最終更新日時が更新されることを確認
        $this->assertNotNull($user->fresh()->last_update_at);
    }

    /**
     * Test sheet store validation with invalid data
     * 異常系: 無効なデータでシート作成時のバリデーションエラーを確認
     */
    public function test_sheet_store_validation_with_invalid_data()
    {
        $user = factory(User::class)->create();

        // 必須フィールドが欠けているデータ
        $invalidData = [
            'groupingName' => 'テスト企業',
            'dataName' => 'テストデータ',
            // 'dataDate' => '2024-01-01', // 必須フィールドが欠けている
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['dataDate']);
    }

    /**
     * Test sheet store with boundary value data
     * 境界値テスト: 境界値データでのシート作成を確認
     */
    public function test_sheet_store_with_boundary_values()
    {
        $user = factory(User::class)->create();

        // 最大長の文字列
        $maxLengthString = str_repeat('あ', 255);

        $accountData = ['account' => PHP_INT_MAX];
        $sheetData = [
            'groupingName' => $maxLengthString,
            'dataName' => $maxLengthString,
            'dataDate' => '1970-01-01', // 最小日付
            'accountJson' => json_encode($accountData), // 大きな数値
            'filteredAccountJson' => json_encode($accountData), // 同じデータ
            'constitutionJson' => json_encode(['assets' => 0]), // 最小値
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $sheetData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('user_input_sheets', [
            'user_id' => $user->id,
            'grouping_name' => $maxLengthString,
            'data_name' => $maxLengthString
        ]);
    }

    /**
     * Test authenticated user can update existing sheet
     * 正常系: 認証済みユーザーが既存のシートを更新できることを確認
     */
    public function test_authenticated_user_can_update_sheet()
    {
        $user = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user->id]);

        $accountData = ['現金' => 2000000];
        $updateData = [
            'groupingName' => '更新された企業名',
            'dataName' => '更新されたデータ名',
            'dataDate' => '2024-12-31',
            'accountJson' => json_encode($accountData),
            'filteredAccountJson' => json_encode($accountData),
            'constitutionJson' => json_encode(['assets' => ********]),
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $response = $this->actingAs($user)
            ->putJson("/sheets/{$sheet->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'userInputSheet' => [
                    'id' => $sheet->id,
                    'groupingName' => '更新された企業名',
                    'dataName' => '更新されたデータ名'
                ]
            ]);

        $this->assertDatabaseHas('user_input_sheets', [
            'id' => $sheet->id,
            'grouping_name' => '更新された企業名',
            'data_name' => '更新されたデータ名'
        ]);
    }

    /**
     * Test user cannot update another user's sheet
     * 異常系: 他のユーザーのシートを更新しようとした場合の認可エラーを確認
     */
    public function test_user_cannot_update_another_users_sheet()
    {
        $user1 = factory(User::class)->create();
        $user2 = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user1->id]);

        $updateData = [
            'groupingName' => '不正な更新',
            'dataName' => '',
            'dataDate' => '2024-01-01',
            'accountJson' => json_encode([]),
            'filteredAccountJson' => json_encode([]),
            'constitutionJson' => json_encode([]),
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $response = $this->actingAs($user2)
            ->putJson("/sheets/{$sheet->id}", $updateData);

        $response->assertStatus(403); // Forbidden
    }

    /**
     * Test authenticated user can delete their sheet
     * 正常系: 認証済みユーザーが自分のシートを削除できることを確認
     */
    public function test_authenticated_user_can_delete_sheet()
    {
        $user = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)
            ->deleteJson("/sheets/{$sheet->id}");

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // ソフトデリートされることを確認
        $this->assertSoftDeleted('user_input_sheets', ['id' => $sheet->id]);

        // ユーザーの最終更新日時が更新されることを確認
        $this->assertNotNull($user->fresh()->last_update_at);
    }

    /**
     * Test user cannot delete another user's sheet
     * 異常系: 他のユーザーのシートを削除しようとした場合の認可エラーを確認
     */
    public function test_user_cannot_delete_another_users_sheet()
    {
        $user1 = factory(User::class)->create();
        $user2 = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user1->id]);

        $response = $this->actingAs($user2)
            ->deleteJson("/sheets/{$sheet->id}");

        $response->assertStatus(403); // Forbidden

        // シートは削除されていないことを確認
        $this->assertDatabaseHas('user_input_sheets', ['id' => $sheet->id]);
    }

    /**
     * Test unauthenticated user cannot access sheet endpoints
     * 異常系: 未認証ユーザーがシートエンドポイントにアクセスできないことを確認
     */
    public function test_unauthenticated_user_cannot_access_sheet_endpoints()
    {
        $sheet = factory(UserInputSheet::class)->create();

        // Store
        $response = $this->postJson('/sheets', []);
        $response->assertStatus(401);

        // Update
        $response = $this->putJson("/sheets/{$sheet->id}", []);
        $response->assertStatus(401);

        // Delete
        $response = $this->deleteJson("/sheets/{$sheet->id}");
        $response->assertStatus(401);
    }

    /**
     * Test sheet store with missing optional fields
     * 正常系: オプショナルフィールドが欠けている場合の動作を確認
     */
    public function test_sheet_store_with_missing_optional_fields()
    {
        $user = factory(User::class)->create();

        $minimalData = [
            'dataDate' => '2024-01-01', // 必須フィールドのみ
            'accountJson' => json_encode([]), // 必須フィールド
            'filteredAccountJson' => json_encode([]), // 必須フィールド
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $minimalData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('user_input_sheets', [
            'user_id' => $user->id,
            'grouping_name' => '', // 空文字がデフォルト
            'data_name' => ''
        ]);
    }

    /**
     * Test sheet validation with invalid date format
     * 異常系: 無効な日付フォーマットでのバリデーションエラーを確認
     */
    public function test_sheet_validation_with_invalid_date_format()
    {
        $user = factory(User::class)->create();

        $invalidDateData = [
            'groupingName' => 'テスト企業',
            'dataName' => 'テストデータ',
            'dataDate' => 'invalid-date-format'
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $invalidDateData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['dataDate']);
    }

    /**
     * Test sheet store with extremely large JSON data
     * パフォーマンステスト: 非常に大きなJSONデータでの動作を確認
     */
    public function test_sheet_store_with_large_json_data()
    {
        $user = factory(User::class)->create();

        // 大量のアカウントデータを生成
        $largeAccountData = [];
        for ($i = 1; $i <= 10000; $i++) {
            $largeAccountData["account_{$i}"] = rand(1000, 100000);
        }

        $sheetData = [
            'groupingName' => 'パフォーマンステスト企業',
            'dataName' => '大量データテスト',
            'dataDate' => '2024-01-01',
            'accountJson' => json_encode($largeAccountData),
            'filteredAccountJson' => json_encode($largeAccountData),
            'constitutionJson' => json_encode(['assets' => array_sum($largeAccountData)]),
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $startTime = microtime(true);
        $response = $this->actingAs($user)
            ->postJson('/sheets', $sheetData);
        $endTime = microtime(true);

        $processingTime = ($endTime - $startTime) * 1000; // ミリ秒

        $response->assertStatus(200);
        $this->assertLessThan(5000, $processingTime); // 5秒以下で完了することを確認
    }

    /**
     * Test concurrent sheet operations
     * パフォーマンステスト: 同時シート操作での動作を確認
     */
    public function test_concurrent_sheet_operations()
    {
        $user = factory(User::class)->create();

        // 複数のシートを同時に作成
        $promises = [];
        for ($i = 1; $i <= 10; $i++) {
            $accountData = ['現金' => $i * 1000000];
            $sheetData = [
                'groupingName' => "同時テスト企業_{$i}",
                'dataName' => "同時テストデータ_{$i}",
                'dataDate' => '2024-01-01',
                'accountJson' => json_encode($accountData),
                'filteredAccountJson' => json_encode($accountData),
                'constitutionJson' => json_encode(['assets' => $i * 5000000]),
                'capitalizeDebtFromPresident' => false,
                'disableChart' => false
            ];

            $response = $this->actingAs($user)
                ->postJson('/sheets', $sheetData);

            $response->assertStatus(200);
        }

        // 作成されたシート数を確認
        $this->assertEquals(10, UserInputSheet::where('user_id', $user->id)->count());
    }

    /**
     * Test sheet store with malformed JSON
     * 異常系: 不正なJSONデータでの動作を確認
     */
    public function test_sheet_store_with_malformed_json()
    {
        $user = factory(User::class)->create();

        $sheetData = [
            'groupingName' => 'テスト企業',
            'dataName' => 'テストデータ',
            'dataDate' => '2024-01-01',
            'accountJson' => 'invalid-json-string', // 不正なJSON
            'filteredAccountJson' => 'invalid-json-string', // 不正なJSON
            'constitutionJson' => json_encode(['assets' => 5000000]),
            'capitalizeDebtFromPresident' => false,
            'disableChart' => false
        ];

        $response = $this->actingAs($user)
            ->postJson('/sheets', $sheetData);

        // アプリケーションレベルでJSONバリデーションが行われる場合
        $response->assertStatus(200); // データベースには文字列として保存される

        $this->assertDatabaseHas('user_input_sheets', [
            'user_id' => $user->id,
            'account_json' => 'invalid-json-string'
        ]);
    }
}
