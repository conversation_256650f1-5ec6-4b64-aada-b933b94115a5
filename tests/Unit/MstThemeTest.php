<?php

namespace Tests\Unit;

use App\Models\MstTheme;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MstThemeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /**
     * Test MstTheme creation with valid data
     * 正常系: 有効なデータでテーマを作成できることを確認
     */
    public function test_mst_theme_can_be_created_with_valid_data()
    {
        $theme = factory(MstTheme::class)->create([
            'color_1' => '#FF0000',
            'color_2' => '#00FF00',
            'color_3' => '#0000FF',
            'color_4' => '#FFFF00'
        ]);

        $this->assertInstanceOf(MstTheme::class, $theme);
        $this->assertEquals('#FF0000', $theme->color_1);
        $this->assertEquals('#00FF00', $theme->color_2);
        $this->assertEquals('#0000FF', $theme->color_3);
        $this->assertEquals('#FFFF00', $theme->color_4);
        $this->assertDatabaseHas('mst_themes', [
            'id' => $theme->id,
            'color_1' => '#FF0000'
        ]);
    }

    /**
     * Test MstTheme has many users relationship
     * 正常系: MstThemeとUserの関係性が正しく動作することを確認
     */
    public function test_mst_theme_has_many_users()
    {
        $theme = factory(MstTheme::class)->create();
        $users = factory(User::class, 3)->create(['mst_theme_id' => $theme->id]);

        $this->assertEquals(3, $theme->users->count());
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $theme->users);

        foreach ($theme->users as $user) {
            $this->assertInstanceOf(User::class, $user);
            $this->assertEquals($theme->id, $user->mst_theme_id);
        }
    }

    /**
     * Test MstTheme color validation format
     * 正常系: カラーコードの形式が正しく保存されることを確認
     */
    public function test_mst_theme_color_format_validation()
    {
        $validColors = [
            '#FFFFFF', // 白
            '#000000', // 黒
            '#FF5733', // オレンジ
            '#33FF57', // 緑
            '#3357FF'  // 青
        ];

        foreach ($validColors as $index => $color) {
            $theme = factory(MstTheme::class)->create([
                'id' => $index + 10,
                'color_1' => $color,
                'color_2' => $color,
                'color_3' => $color
            ]);

            $this->assertEquals($color, $theme->color_1);
            $this->assertEquals($color, $theme->color_2);
            $this->assertEquals($color, $theme->color_3);
        }
    }

    /**
     * Test MstTheme soft delete functionality
     * 正常系: ソフトデリート機能が正しく動作することを確認
     */
    public function test_mst_theme_soft_delete()
    {
        $theme = factory(MstTheme::class)->create(['id' => 99]);

        // ソフトデリート実行
        $theme->delete();

        // データベースには存在するが、通常のクエリでは取得できない
        $this->assertDatabaseHas('mst_themes', ['id' => 99]);
        $this->assertNull(MstTheme::find(99));

        // withTrashedで取得可能
        $trashedTheme = MstTheme::withTrashed()->find(99);
        $this->assertNotNull($trashedTheme);
        $this->assertNotNull($trashedTheme->deleted_at);
    }

    /**
     * Test MstTheme restore functionality
     * 正常系: ソフトデリートからの復元が正しく動作することを確認
     */
    public function test_mst_theme_restore()
    {
        $theme = factory(MstTheme::class)->create(['id' => 98]);

        // ソフトデリート実行
        $theme->delete();
        $this->assertNull(MstTheme::find(98));

        // 復元
        MstTheme::withTrashed()->find(98)->restore();

        // 復元後は通常のクエリで取得可能
        $restoredTheme = MstTheme::find(98);
        $this->assertNotNull($restoredTheme);
        $this->assertNull($restoredTheme->deleted_at);
    }

    /**
     * Test MstTheme with duplicate ID
     * 異常系: 重複したIDでテーマを作成しようとした場合の動作を確認
     */
    public function test_mst_theme_duplicate_id_constraint()
    {
        factory(MstTheme::class)->create(['id' => 50]);

        $this->expectException(\Illuminate\Database\QueryException::class);

        MstTheme::create([
            'id' => 50, // 既存のIDと同じ
            'color_1' => '#FF0000',
            'color_2' => '#00FF00',
            'color_3' => '#0000FF'
        ]);
    }

    /**
     * Test MstTheme ID boundary values
     * 境界値テスト: tinyIntegerの境界値での動作を確認
     */
    public function test_mst_theme_id_boundary_values()
    {
        // 既存の最小値を確認（シードデータから）
        $existingTheme = MstTheme::find(1);
        $this->assertNotNull($existingTheme);
        $this->assertEquals(1, $existingTheme->id);

        // 最大値（255 - tinyInteger unsigned）
        $theme2 = factory(MstTheme::class)->create(['id' => 255]);
        $this->assertEquals(255, $theme2->id);
    }

    /**
     * Test MstTheme with invalid color format
     * 異常系: 無効なカラーフォーマットでの動作を確認
     */
    public function test_mst_theme_with_invalid_color_format()
    {
        // データベースレベルでは文字列として保存されるため、
        // 実際の検証はアプリケーションレベルで行われる
        $theme = factory(MstTheme::class)->create([
            'color_1' => 'invalid-color',
            'color_2' => '123456', // #が無い
            'color_3' => '#GGGGGG' // 無効な16進数
        ]);

        // データベースには保存されるが、アプリケーションで検証される
        $this->assertEquals('invalid-color', $theme->color_1);
        $this->assertEquals('123456', $theme->color_2);
        $this->assertEquals('#GGGGGG', $theme->color_3);
    }

    /**
     * Test MstTheme with users cascade behavior
     * 正常系: テーマ削除時のユーザーとの関係を確認
     */
    public function test_mst_theme_with_users_cascade_behavior()
    {
        $theme = factory(MstTheme::class)->create(['id' => 77]);
        $user = factory(User::class)->create(['mst_theme_id' => $theme->id]);

        // テーマを削除
        $theme->delete();

        // ユーザーは残存するが、テーマとの関係は null になる
        $this->assertNotNull(User::find($user->id));

        // リレーションは null を返す
        $refreshedUser = User::find($user->id);
        $this->assertNull($refreshedUser->theme);
    }

    /**
     * Test MstTheme timestamps functionality
     * 正常系: タイムスタンプが正しく設定されることを確認
     */
    public function test_mst_theme_timestamps()
    {
        $theme = factory(MstTheme::class)->create();

        $this->assertNotNull($theme->created_at);
        $this->assertNotNull($theme->updated_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $theme->created_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $theme->updated_at);

        // 更新時にupdated_atが変更されることを確認
        $originalUpdatedAt = $theme->updated_at;
        sleep(1);
        $theme->update(['color_1' => '#FFFFFF']);

        $this->assertNotEquals($originalUpdatedAt, $theme->fresh()->updated_at);
    }

    /**
     * Test MstTheme performance with large dataset
     * パフォーマンステスト: 大量データでの検索性能を確認
     */
    public function test_mst_theme_query_performance()
    {
        // 複数のテーマを作成
        for ($i = 100; $i <= 150; $i++) {
            factory(MstTheme::class)->create(['id' => $i]);
        }

        // クエリ実行時間の測定
        $startTime = microtime(true);
        $themes = MstTheme::all();
        $endTime = microtime(true);

        $queryTime = ($endTime - $startTime) * 1000; // ミリ秒

        $this->assertGreaterThanOrEqual(51, $themes->count()); // 作成したテーマ数以上
        $this->assertLessThan(100, $queryTime); // 100ms以下で完了することを確認
    }

    /**
     * Test MstTheme color hex validation
     * 境界値テスト: 16進数カラーコードの境界値での動作を確認
     */
    public function test_mst_theme_hex_color_boundaries()
    {
        // 最小値（#000000）
        $theme1 = factory(MstTheme::class)->create([
            'color_1' => '#000000',
            'color_2' => '#000000',
            'color_3' => '#000000'
        ]);
        $this->assertEquals('#000000', $theme1->color_1);

        // 最大値（#FFFFFF）
        $theme2 = factory(MstTheme::class)->create([
            'id' => 251,
            'color_1' => '#FFFFFF',
            'color_2' => '#FFFFFF',
            'color_3' => '#FFFFFF'
        ]);
        $this->assertEquals('#FFFFFF', $theme2->color_1);

        // 小文字での16進数
        $theme3 = factory(MstTheme::class)->create([
            'id' => 252,
            'color_1' => '#abcdef',
            'color_2' => '#123456',
            'color_3' => '#fedcba'
        ]);
        $this->assertEquals('#abcdef', $theme3->color_1);
    }

    /**
     * Test MstTheme memory usage
     * パフォーマンステスト: メモリ使用量の確認
     */
    public function test_mst_theme_memory_usage()
    {
        $memoryBefore = memory_get_usage();

        // 多数のテーマを作成
        for ($i = 200; $i <= 250; $i++) {
            factory(MstTheme::class)->create(['id' => $i]);
        }

        $memoryAfter = memory_get_usage();
        $memoryUsed = $memoryAfter - $memoryBefore;

        // メモリ使用量が過度に大きくないことを確認（5MB以下）
        $this->assertLessThan(5 * 1024 * 1024, $memoryUsed);
    }
}
