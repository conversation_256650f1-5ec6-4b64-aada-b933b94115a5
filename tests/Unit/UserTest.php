<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\MstTheme;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /**
     * Test user creation with valid data
     * 正常系: 有効なデータでユーザーを作成できることを確認
     */
    public function test_user_can_be_created_with_valid_data()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'company_name' => 'Test Company',
            'account_id' => 'test-account',
            'mst_theme_id' => 1,
            'start_at' => now(),
            'end_at' => now()->addDays(30),
            'show_link' => true,
            'show_pdf_company_name' => true,
            'max_input_sheets' => 10
        ];

        $user = User::create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Test Company', $user->company_name);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'company_name' => 'Test Company'
        ]);
    }

    /**
     * Test user theme relationship
     * 正常系: ユーザーとテーマの関係性が正しく動作することを確認
     */
    public function test_user_has_theme_relationship()
    {
        $user = factory(User::class)->create(['mst_theme_id' => 1]);

        $this->assertInstanceOf(MstTheme::class, $user->theme);
        $this->assertEquals(1, $user->theme->id);
    }

    /**
     * Test listed company info link accessor
     * 正常系: 上場情報リンクのアクセサが正しく動作することを確認
     */
    public function test_listed_company_info_link_accessor()
    {
        $user = factory(User::class)->create();

        $this->assertIsString($user->listed_company_info_link);
        $this->assertEquals(config('listedCompanyInfoLinks.link'), $user->listed_company_info_link);
    }

    /**
     * Test user dates are cast to Carbon instances
     * 正常系: 日付フィールドがCarbon インスタンスにキャストされることを確認
     */
    public function test_user_dates_are_cast_to_carbon()
    {
        $user = factory(User::class)->create([
            'start_at' => '2024-01-01 00:00:00',
            'end_at' => '2024-12-31 23:59:59',
            'last_login_at' => '2024-06-15 12:30:00'
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $user->start_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->end_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->last_login_at);
    }

    /**
     * Test user with eager loaded theme
     * 正常系: テーマが自動的にロードされることを確認
     */
    public function test_user_loads_theme_automatically()
    {
        $user = factory(User::class)->create(['mst_theme_id' => 1]);

        // データベースから再取得してテーマが自動的にロードされることを確認
        $freshUser = User::find($user->id);
        $this->assertTrue($freshUser->relationLoaded('theme'));
        $this->assertNotNull($freshUser->theme);
        $this->assertEquals(1, $freshUser->theme->id);
    }

    /**
     * Test user attributes are properly hidden
     * 正常系: 機密情報が配列から隠されることを確認
     */
    public function test_user_hidden_attributes()
    {
        $user = factory(User::class)->create();

        $userArray = $user->toArray();

        $this->assertArrayNotHasKey('password', $userArray);
        $this->assertArrayNotHasKey('remember_token', $userArray);
    }

    /**
     * Test user with invalid theme ID
     * 異常系: 無効なテーマIDでユーザーを作成した場合の動作を確認
     */
    public function test_user_with_invalid_theme_id()
    {
        $user = factory(User::class)->create(['mst_theme_id' => 999]);

        $this->assertNull($user->theme);
    }

    /**
     * Test user boundary values for max_input_sheets
     * 境界値テスト: max_input_sheetsの境界値での動作を確認
     */
    public function test_user_max_input_sheets_boundary_values()
    {
        // 0の場合
        $user1 = factory(User::class)->create(['max_input_sheets' => 0]);
        $this->assertEquals(0, $user1->max_input_sheets);

        // 大きな値の場合
        $user2 = factory(User::class)->create(['max_input_sheets' => 1000]);
        $this->assertEquals(1000, $user2->max_input_sheets);

        // 0の場合（無限）
        $user3 = factory(User::class)->create(['max_input_sheets' => 0]);
        $this->assertEquals(0, $user3->max_input_sheets);
    }

    /**
     * Test user date validation boundary cases
     * 境界値テスト: 日付フィールドの境界値での動作を確認
     */
    public function test_user_date_boundary_cases()
    {
        // 同じ日付の場合
        $sameDate = now();
        $user1 = factory(User::class)->create([
            'start_at' => $sameDate,
            'end_at' => $sameDate
        ]);
        $this->assertEquals($sameDate->toDateString(), $user1->start_at->toDateString());
        $this->assertEquals($sameDate->toDateString(), $user1->end_at->toDateString());

        // 逆転した日付の場合（データベースレベルではエラーにならない）
        $user2 = factory(User::class)->create([
            'start_at' => now()->addDays(1),
            'end_at' => now()
        ]);
        $this->assertGreaterThan($user2->end_at, $user2->start_at);
    }
}
