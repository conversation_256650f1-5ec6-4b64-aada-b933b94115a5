<?php

namespace Tests\Unit;

use App\Models\Administrator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AdministratorTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /**
     * Test Administrator creation with valid data
     * 正常系: 有効なデータで管理者を作成できることを確認
     */
    public function test_administrator_can_be_created_with_valid_data()
    {
        $adminData = [
            'name' => 'Test Administrator',
            'account_id' => 'test-admin',
            'password' => Hash::make('password123')
        ];

        $admin = Administrator::create($adminData);

        $this->assertInstanceOf(Administrator::class, $admin);
        $this->assertEquals('Test Administrator', $admin->name);
        $this->assertEquals('test-admin', $admin->account_id);
        $this->assertDatabaseHas('administrators', [
            'name' => 'Test Administrator',
            'account_id' => 'test-admin'
        ]);
    }

    /**
     * Test Administrator password is hidden from array output
     * 正常系: パスワードが配列出力から隠されることを確認
     */
    public function test_administrator_password_is_hidden()
    {
        $admin = factory(Administrator::class)->create();

        $adminArray = $admin->toArray();

        $this->assertArrayNotHasKey('password', $adminArray);
        $this->assertArrayHasKey('name', $adminArray);
        $this->assertArrayHasKey('account_id', $adminArray);
    }

    /**
     * Test Administrator unique account_id constraint
     * 異常系: 重複したaccount_idで管理者を作成しようとした場合の動作を確認
     */
    public function test_administrator_unique_account_id_constraint()
    {
        $existingAdmin = factory(Administrator::class)->create(['account_id' => 'unique-admin']);

        $this->expectException(\Illuminate\Database\QueryException::class);

        Administrator::create([
            'name' => 'Another Admin',
            'account_id' => 'unique-admin', // 既存のaccount_idと同じ
            'password' => Hash::make('password123')
        ]);
    }

    /**
     * Test Administrator authentication functionality
     * 正常系: 管理者認証機能が正しく動作することを確認
     */
    public function test_administrator_authentication()
    {
        $password = 'test-password';
        $admin = factory(Administrator::class)->create([
            'password' => Hash::make($password)
        ]);

        // パスワード検証
        $this->assertTrue(Hash::check($password, $admin->password));
        $this->assertFalse(Hash::check('wrong-password', $admin->password));
    }

    /**
     * Test Administrator soft delete functionality
     * 正常系: ソフトデリート機能が正しく動作することを確認
     */
    public function test_administrator_soft_delete()
    {
        $admin = factory(Administrator::class)->create();
        $adminId = $admin->id;

        // ソフトデリート実行
        $admin->delete();

        // データベースには存在するが、通常のクエリでは取得できない
        $this->assertDatabaseHas('administrators', ['id' => $adminId]);
        $this->assertNull(Administrator::find($adminId));

        // withTrashedで取得可能
        $trashedAdmin = Administrator::withTrashed()->find($adminId);
        $this->assertNotNull($trashedAdmin);
        $this->assertNotNull($trashedAdmin->deleted_at);
    }

    /**
     * Test Administrator restore functionality
     * 正常系: ソフトデリートからの復元が正しく動作することを確認
     */
    public function test_administrator_restore()
    {
        $admin = factory(Administrator::class)->create();
        $adminId = $admin->id;

        // ソフトデリート実行
        $admin->delete();
        $this->assertNull(Administrator::find($adminId));

        // 復元
        Administrator::withTrashed()->find($adminId)->restore();

        // 復元後は通常のクエリで取得可能
        $restoredAdmin = Administrator::find($adminId);
        $this->assertNotNull($restoredAdmin);
        $this->assertNull($restoredAdmin->deleted_at);
    }

    /**
     * Test Administrator with empty name
     * 異常系: 空の名前で管理者を作成した場合の動作を確認
     */
    public function test_administrator_with_empty_name()
    {
        $admin = Administrator::create([
            'name' => '', // 空の名前
            'account_id' => 'test-admin',
            'password' => Hash::make('password123')
        ]);

        // 空の名前でも作成されるが、名前が空であることを確認
        $this->assertEquals('', $admin->name);
        $this->assertEquals('test-admin', $admin->account_id);
    }

    /**
     * Test Administrator account_id boundary values
     * 境界値テスト: account_idの境界値での動作を確認
     */
    public function test_administrator_account_id_boundary_values()
    {
        // 最小長（1文字）
        $admin1 = factory(Administrator::class)->create(['account_id' => 'a']);
        $this->assertEquals('a', $admin1->account_id);

        // 長い文字列（255文字）
        $longAccountId = str_repeat('a', 255);
        $admin2 = factory(Administrator::class)->create(['account_id' => $longAccountId]);
        $this->assertEquals($longAccountId, $admin2->account_id);
    }

    /**
     * Test Administrator name with Japanese characters
     * 境界値テスト: 日本語文字を含む名前での動作を確認
     */
    public function test_administrator_name_with_japanese_characters()
    {
        $japaneseName = '管理者田中';
        $admin = factory(Administrator::class)->create(['name' => $japaneseName]);

        $this->assertEquals($japaneseName, $admin->name);
        $this->assertDatabaseHas('administrators', ['name' => $japaneseName]);
    }

    /**
     * Test Administrator timestamps are properly set
     * 正常系: タイムスタンプが正しく設定されることを確認
     */
    public function test_administrator_timestamps()
    {
        $admin = factory(Administrator::class)->create();

        $this->assertNotNull($admin->created_at);
        $this->assertNotNull($admin->updated_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $admin->created_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $admin->updated_at);
    }

    /**
     * Test Administrator search functionality
     * パフォーマンステスト: 管理者検索機能の性能を確認
     */
    public function test_administrator_search_performance()
    {
        // 複数の管理者を作成
        factory(Administrator::class, 50)->create();

        // 検索クエリの実行時間測定
        $startTime = microtime(true);
        $results = Administrator::where('name', 'like', '%Test%')->get();
        $endTime = microtime(true);

        $queryTime = ($endTime - $startTime) * 1000; // ミリ秒

        $this->assertLessThan(500, $queryTime); // 500ms以下で完了することを確認
    }

    /**
     * Test Administrator memory usage with large dataset
     * パフォーマンステスト: 大量データでのメモリ使用量を確認
     */
    public function test_administrator_memory_usage()
    {
        $memoryBefore = memory_get_usage();

        // 大量の管理者データを作成
        factory(Administrator::class, 1000)->create();

        $memoryAfter = memory_get_usage();
        $memoryUsed = $memoryAfter - $memoryBefore;

        // メモリ使用量が過度に大きくないことを確認（20MB以下）
        $this->assertLessThan(20 * 1024 * 1024, $memoryUsed);
    }

    /**
     * Test Administrator with various password complexities
     * 境界値テスト: 様々なパスワード複雑度での動作を確認
     */
    public function test_administrator_password_complexity()
    {
        // 短いパスワード
        $admin1 = factory(Administrator::class)->create([
            'password' => Hash::make('123')
        ]);
        $this->assertTrue(Hash::check('123', $admin1->password));

        // 長いパスワード
        $longPassword = str_repeat('a', 255);
        $admin2 = factory(Administrator::class)->create([
            'password' => Hash::make($longPassword)
        ]);
        $this->assertTrue(Hash::check($longPassword, $admin2->password));

        // 特殊文字を含むパスワード
        $specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        $admin3 = factory(Administrator::class)->create([
            'password' => Hash::make($specialPassword)
        ]);
        $this->assertTrue(Hash::check($specialPassword, $admin3->password));
    }
}
