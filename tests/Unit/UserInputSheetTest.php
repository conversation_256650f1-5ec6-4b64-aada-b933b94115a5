<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserInputSheet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class UserInputSheetTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    /**
     * Test UserInputSheet creation with valid data
     * 正常系: 有効なデータで入力シートを作成できることを確認
     */
    public function test_user_input_sheet_can_be_created_with_valid_data()
    {
        $user = factory(User::class)->create();

        $accountData = ['accounts' => ['現金' => 1000000]];
        $sheetData = [
            'user_id' => $user->id,
            'grouping_name' => 'テスト企業',
            'data_name' => 'テストデータ',
            'data_date' => '2024-01-01',
            'account_json' => json_encode($accountData),
            'filtered_account_json' => json_encode($accountData),
            'constitution_json' => json_encode(['constitution' => ['assets' => 5000000]])
        ];

        $sheet = UserInputSheet::create($sheetData);

        $this->assertInstanceOf(UserInputSheet::class, $sheet);
        $this->assertEquals($user->id, $sheet->user_id);
        $this->assertEquals('テスト企業', $sheet->grouping_name);
        $this->assertEquals('テストデータ', $sheet->data_name);
        $this->assertDatabaseHas('user_input_sheets', [
            'user_id' => $user->id,
            'grouping_name' => 'テスト企業'
        ]);
    }

    /**
     * Test UserInputSheet belongs to User relationship
     * 正常系: UserInputSheetとUserの関係性が正しく動作することを確認
     */
    public function test_user_input_sheet_belongs_to_user()
    {
        $user = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $sheet->user);
        $this->assertEquals($user->id, $sheet->user->id);
    }

    /**
     * Test UserInputSheet soft delete functionality
     * 正常系: ソフトデリート機能が正しく動作することを確認
     */
    public function test_user_input_sheet_soft_delete()
    {
        $sheet = factory(UserInputSheet::class)->create();
        $sheetId = $sheet->id;

        // ソフトデリート実行
        $sheet->delete();

        // データベースには存在するが、通常のクエリでは取得できない
        $this->assertDatabaseHas('user_input_sheets', ['id' => $sheetId]);
        $this->assertNull(UserInputSheet::find($sheetId));

        // withTrashedで取得可能
        $trashedSheet = UserInputSheet::withTrashed()->find($sheetId);
        $this->assertNotNull($trashedSheet);
        $this->assertNotNull($trashedSheet->deleted_at);
    }

    /**
     * Test UserInputSheet restore functionality
     * 正常系: ソフトデリートからの復元が正しく動作することを確認
     */
    public function test_user_input_sheet_restore()
    {
        $sheet = factory(UserInputSheet::class)->create();
        $sheetId = $sheet->id;

        // ソフトデリート実行
        $sheet->delete();
        $this->assertNull(UserInputSheet::find($sheetId));

        // 復元
        UserInputSheet::withTrashed()->find($sheetId)->restore();

        // 復元後は通常のクエリで取得可能
        $restoredSheet = UserInputSheet::find($sheetId);
        $this->assertNotNull($restoredSheet);
        $this->assertNull($restoredSheet->deleted_at);
    }

    /**
     * Test UserInputSheet JSON field validation
     * 正常系: JSONフィールドが正しく処理されることを確認
     */
    public function test_user_input_sheet_json_fields()
    {
        $accountData = ['現金' => 1000000, '売掛金' => 500000];
        $constitutionData = ['assets' => 5000000, 'liabilities' => 2000000];

        $sheet = factory(UserInputSheet::class)->create([
            'account_json' => json_encode($accountData),
            'constitution_json' => json_encode($constitutionData)
        ]);

        $this->assertEquals($accountData, json_decode($sheet->account_json, true));
        $this->assertEquals($constitutionData, json_decode($sheet->constitution_json, true));
    }

    /**
     * Test UserInputSheet date field casting
     * 正常系: 日付フィールドが正しくキャストされることを確認
     */
    public function test_user_input_sheet_date_casting()
    {
        $sheet = factory(UserInputSheet::class)->create([
            'data_date' => '2024-01-01'
        ]);

        $this->assertInstanceOf(Carbon::class, $sheet->data_date);
        $this->assertEquals('2024-01-01', $sheet->data_date->format('Y-m-d'));
    }

    /**
     * Test UserInputSheet with invalid user_id
     * 異常系: 無効なuser_idで作成しようとした場合の動作を確認
     */
    public function test_user_input_sheet_with_invalid_user_id()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        UserInputSheet::create([
            'user_id' => 999999, // 存在しないユーザーID
            'grouping_name' => 'テスト企業',
            'data_name' => 'テストデータ',
            'data_date' => '2024-01-01',
            'account_json' => json_encode(['現金' => 1000000]),
            'constitution_json' => json_encode(['assets' => 5000000])
        ]);
    }

    /**
     * Test UserInputSheet with empty JSON fields
     * 境界値テスト: 空のJSONフィールドでの動作を確認
     */
    public function test_user_input_sheet_with_empty_json()
    {
        $user = factory(User::class)->create();

        $sheet = UserInputSheet::create([
            'user_id' => $user->id,
            'grouping_name' => 'テスト企業',
            'data_name' => 'テストデータ',
            'data_date' => '2024-01-01',
            'account_json' => json_encode([]),
            'filtered_account_json' => json_encode([]),
            'constitution_json' => json_encode([])
        ]);

        $this->assertEquals([], json_decode($sheet->account_json, true));
        $this->assertEquals([], json_decode($sheet->constitution_json, true));
    }

    /**
     * Test UserInputSheet with large JSON data
     * パフォーマンステスト: 大きなJSONデータでの動作を確認
     */
    public function test_user_input_sheet_with_large_json_data()
    {
        $user = factory(User::class)->create();

        // 大量のアカウントデータを生成
        $largeAccountData = [];
        for ($i = 1; $i <= 1000; $i++) {
            $largeAccountData["account_{$i}"] = rand(1000, 100000);
        }

        $sheet = UserInputSheet::create([
            'user_id' => $user->id,
            'grouping_name' => 'テスト企業',
            'data_name' => '大量データテスト',
            'data_date' => '2024-01-01',
            'account_json' => json_encode($largeAccountData),
            'filtered_account_json' => json_encode($largeAccountData),
            'constitution_json' => json_encode(['assets' => array_sum($largeAccountData)])
        ]);

        $this->assertInstanceOf(UserInputSheet::class, $sheet);
        $this->assertEquals(1000, count(json_decode($sheet->account_json, true)));

        // メモリ使用量の確認（おおよそ）
        $memoryUsed = memory_get_usage();
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed); // 50MB以下
    }

    /**
     * Test UserInputSheet date boundary values
     * 境界値テスト: 日付フィールドの境界値での動作を確認
     */
    public function test_user_input_sheet_date_boundary_values()
    {
        $user = factory(User::class)->create();

        // 最小日付
        $sheet1 = factory(UserInputSheet::class)->create([
            'user_id' => $user->id,
            'data_date' => '1970-01-01'
        ]);
        $this->assertEquals('1970-01-01', $sheet1->data_date->format('Y-m-d'));

        // 最大日付（実用的な範囲）
        $sheet2 = factory(UserInputSheet::class)->create([
            'user_id' => $user->id,
            'data_date' => '2099-12-31'
        ]);
        $this->assertEquals('2099-12-31', $sheet2->data_date->format('Y-m-d'));
    }

    /**
     * Test UserInputSheet string field length boundaries
     * 境界値テスト: 文字列フィールドの長さ境界での動作を確認
     */
    public function test_user_input_sheet_string_length_boundaries()
    {
        $user = factory(User::class)->create();

        // 長い文字列での作成
        $longString = str_repeat('あ', 255); // 255文字の日本語文字列

        $sheet = factory(UserInputSheet::class)->create([
            'user_id' => $user->id,
            'grouping_name' => $longString,
            'data_name' => $longString
        ]);

        $this->assertEquals($longString, $sheet->grouping_name);
        $this->assertEquals($longString, $sheet->data_name);
    }

    /**
     * Test UserInputSheet query performance with multiple records
     * パフォーマンステスト: 複数レコードでのクエリ性能を確認
     */
    public function test_user_input_sheet_query_performance()
    {
        $user = factory(User::class)->create();

        // 複数のシートを作成
        $sheets = factory(UserInputSheet::class, 100)->create([
            'user_id' => $user->id
        ]);

        // クエリ実行時間の測定
        $startTime = microtime(true);
        $retrievedSheets = UserInputSheet::where('user_id', $user->id)->get();
        $endTime = microtime(true);

        $queryTime = ($endTime - $startTime) * 1000; // ミリ秒

        $this->assertEquals(100, $retrievedSheets->count());
        $this->assertLessThan(1000, $queryTime); // 1秒以下で完了することを確認
    }

    /**
     * Test user_id cast to integer
     * user_idが正しくintegerにキャストされることを確認
     */
    public function test_user_id_is_cast_to_integer()
    {
        $user = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user->id]);

        // user_idがinteger型であることを確認
        $this->assertIsInt($sheet->user_id);
        $this->assertSame($user->id, $sheet->user_id);
    }

    /**
     * Test user_id type consistency between User and UserInputSheet
     * UserとUserInputSheetのID型が一致することを確認
     */
    public function test_user_id_type_consistency()
    {
        $user = factory(User::class)->create();
        $sheet = factory(UserInputSheet::class)->create(['user_id' => $user->id]);

        // 両方のIDが同じ型であることを確認
        $this->assertSame(gettype($user->id), gettype($sheet->user_id));

        // 厳密比較が成功することを確認
        $this->assertTrue($user->id === $sheet->user_id);
    }

    /**
     * Test user_id assignment with string value
     * 文字列値でuser_idを設定してもintegerにキャストされることを確認
     */
    public function test_user_id_string_assignment_casted_to_integer()
    {
        $user = factory(User::class)->create();

        // 文字列でuser_idを設定
        $sheet = new UserInputSheet();
        $sheet->user_id = (string)$user->id; // 明示的に文字列に変換
        $sheet->grouping_name = 'テスト企業';
        $sheet->data_name = 'テストデータ';
        $sheet->data_date = '2024-01-01';
        $sheet->account_json = json_encode([]);
        $sheet->filtered_account_json = json_encode([]);
        $sheet->constitution_json = json_encode([]);
        $sheet->save();

        // データベースから再取得
        $sheet = $sheet->fresh();

        // user_idがintegerにキャストされていることを確認
        $this->assertIsInt($sheet->user_id);
        $this->assertSame($user->id, $sheet->user_id);
    }
}
