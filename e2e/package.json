{"name": "e2e", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"setup": "npm install && npx playwright install && npx playwright install-deps", "test": "playwright test", "test:ui": "playwright test --ui --ui-host=0.0.0.0 --ui-port=9323", "test:headed": "playwright test --headed", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "report": "playwright show-report --host=0.0.0.0 --port=9323", "report:serve": "npx http-server playwright-report -p 9323 -a 0.0.0.0", "clean": "rm -rf playwright-report test-results"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.55.0", "@types/node": "^24.3.0", "http-server": "^14.1.1"}}