import { expect, test as setup } from "@playwright/test";

const authFile = ".auth/user.json";

setup.describe("Authentication Setup", () => {
	setup("authenticate", async ({ page }) => {
		await page.goto("/login");
		await page.fill('input[name="account_id"]', "test-user");
		await page.fill('input[name="password"]', "testpass123");
		await page.click('button:has-text("ログイン")');

		await page.waitForURL("/");
		const hasErrorMessages =
			(await page.locator(".el-form-item__error").count()) > 0;
		await expect(hasErrorMessages).toBe(false);

		if (!hasErrorMessages) {
			await page.context().storageState({ path: authFile });
		}
	});
});
