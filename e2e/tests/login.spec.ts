import { expect, test } from "@playwright/test";

// Not using existing auth state
test.use({ storageState: { cookies: [], origins: [] } });

test.describe("User Authentication", () => {
	test("login form renders correctly", async ({ page }) => {
		// Navigate to login page
		await page.goto("/login");

		// Verify we're on the login page with correct title
		await expect(page).toHaveTitle(/キャッシュ体質図/);

		// Verify login form elements are present
		await expect(page.locator('input[name="account_id"]')).toBeVisible();
		await expect(page.locator('input[name="password"]')).toBeVisible();
		await expect(
			page.locator("button").filter({ hasText: "ログイン" }),
		).toBeVisible();

		// Verify form labels are present
		await expect(page.locator("text=アカウントID")).toBeVisible();
		await expect(page.locator("text=パスワード")).toBeVisible();
	});

	test("authorized user can attempt login (test with known credentials)", async ({
		page,
	}) => {
		// Navigate to login page
		await page.goto("/login");

		// Fill in test credentials (from TestDataSeeder)
		await page.fill('input[name="account_id"]', "test-user");
		await page.fill('input[name="password"]', "testpass123");

		// Click login button
		await page.click('button:has-text("ログイン")');

		// Wait for form submission
		await page.waitForTimeout(3000);

		// Check the result - either success (redirect) or validation error
		const currentUrl = page.url();
		const hasErrorMessages =
			(await page.locator(".el-form-item__error").count()) > 0;
		expect(currentUrl).not.toMatch(/\/login/);
		expect(hasErrorMessages).toBe(false);
	});

	test("unauthorized user cannot login", async ({ page }) => {
		// Navigate to login page
		await page.goto("/login");

		// Verify login form elements are present
		await expect(page.locator('input[name="account_id"]')).toBeVisible();
		await expect(page.locator('input[name="password"]')).toBeVisible();

		// Fill in invalid credentials
		await page.fill('input[name="account_id"]', "invaliduser");
		await page.fill('input[name="password"]', "wrongpassword");

		// Click login button
		await page.click('button:has-text("ログイン")');

		// Wait for form submission to complete
		await page.waitForTimeout(2000);

		// Verify we remain on login page
		await expect(page).toHaveURL("/login");

		// Verify login form is still visible (indicating login failed)
		await expect(page.locator('input[name="account_id"]')).toBeVisible();
		await expect(page.locator('input[name="password"]')).toBeVisible();

		// Check for error messages (Laravel validation errors)
		const errorElements = page.locator(".el-form-item__error");
		await expect(errorElements.first()).toBeVisible();
	});

	test("expired user cannot login", async ({ page }) => {
		// Navigate to login page
		await page.goto("/login");

		// Fill in expired user credentials (from TestDataSeeder)
		await page.fill('input[name="account_id"]', "test-expired");
		await page.fill('input[name="password"]', "testpass123");

		// Click login button
		await page.click('button:has-text("ログイン")');

		// Wait for form submission
		await page.waitForTimeout(3000);

		// Should remain on login page or show expired message
		const currentUrl = page.url();
		if (currentUrl.includes("/login")) {
			// Check for expired account indicators
			const hasErrorMessages =
				(await page.locator(".el-form-item__error").count()) > 0;
			const hasExpiredAlert =
				(await page.locator(".el-alert--error").count()) > 0;

			expect(hasErrorMessages || hasExpiredAlert).toBe(true);
		}
	});

	test("login form validation displays error messages", async ({ page }) => {
		// Navigate to login page
		await page.goto("/login");

		// Try to submit empty form
		await page.click('button:has-text("ログイン")');

		// Wait for form submission to complete
		await page.waitForTimeout(2000);

		// Verify we remain on login page
		await expect(page).toHaveURL("/login");

		// Verify form is still visible
		await expect(page.locator('input[name="account_id"]')).toBeVisible();
		await expect(page.locator('input[name="password"]')).toBeVisible();
	});

	test("admin user can access admin login", async ({ page }) => {
		// Navigate to admin login page
		await page.goto("/manage/login");

		// Verify we're on the admin login page
		await expect(page).toHaveTitle(/キャッシュ体質図/);

		// Verify admin login form elements are present
		await expect(page.locator('input[name="account_id"]')).toBeVisible();
		await expect(page.locator('input[name="password"]')).toBeVisible();
		await expect(
			page.locator("button").filter({ hasText: "ログイン" }),
		).toBeVisible();

		// Fill in admin test credentials (from TestDataSeeder)
		await page.fill('input[name="account_id"]', "test-admin");
		await page.fill('input[name="password"]', "adminpass123");

		// Click login button
		await page.click('button:has-text("ログイン")');

		// Wait for form submission
		await page.waitForTimeout(3000);

		// Check the result - either success or validation error
		const currentUrl = page.url();
		const hasErrorMessages =
			(await page.locator(".el-form-item__error").count()) > 0;

		expect(currentUrl).not.toMatch(/\/manage\/login/);
		expect(hasErrorMessages).toBe(false);
	});
});
