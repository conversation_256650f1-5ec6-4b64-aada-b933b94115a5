import { expect, test } from "@playwright/test";

test.describe("Login Test", () => {
	test("can login", async ({ page }) => {
		await page.goto("/");

		// Menu check
		await expect(page.locator('li[role="menuitem"]').first()).toHaveText(
			"新規入力",
		);
		await expect(page.locator('li[role="menuitem"]').first()).toHaveClass(
			"el-menu-item is-active",
		);

		// Form check
		await expect(page.locator("label.el-form-item__label").nth(0)).toHaveText(
			"企業名",
		);
		await expect(page.locator("label.el-form-item__label").nth(1)).toHaveText(
			"対象月",
		);
		await expect(page.locator("label.el-form-item__label").nth(2)).toHaveText(
			"タイトル",
		);
	});
});
