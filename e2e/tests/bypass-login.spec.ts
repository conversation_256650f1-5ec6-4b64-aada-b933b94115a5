import { test, expect } from '@playwright/test';

test.describe('Bypass Login Access', () => {
  test('can access home page with session cookie simulation', async ({ page, context }) => {
    // Try to simulate an authenticated session using cookies
    // This is a more direct approach to bypassing login

    // First, let's try to set a session cookie (this may not work without valid Laravel session)
    await context.addCookies([{
      name: 'laravel_session',
      value: 'test_session_value',
      domain: 'web',
      path: '/',
      httpOnly: true
    }]);

    // Also set a remember token cookie if needed
    await context.addCookies([{
      name: 'remember_web_token',
      value: 'test_remember_token',
      domain: 'web',
      path: '/',
      httpOnly: true
    }]);

    // Now try to access the home page
    await page.goto('/');

    // Wait a moment for any redirects to occur
    await page.waitForTimeout(2000);

    const currentUrl = page.url();

    if (currentUrl.includes('/login')) {
      console.log('Cookie simulation failed - still redirected to login');
      console.log('This is expected behavior as we used dummy cookie values');

      // Verify login page is correctly displayed
      await expect(page).toHaveURL('/login');
      await expect(page).toHaveTitle(/キャッシュ体質図/);
      await expect(page.locator('input[name="account_id"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();

    } else if (currentUrl === '/') {
      console.log('Cookie simulation succeeded - accessed home page directly');

      // Verify we successfully bypassed login
      await expect(page).toHaveURL('/');
      await expect(page).toHaveTitle(/キャッシュ体質図/);
      await expect(page.locator('#app')).toBeVisible();

      // Ensure we don't see login form elements
      await expect(page.locator('input[name="account_id"]')).not.toBeVisible();

    } else {
      console.log(`Unexpected URL after cookie simulation: ${currentUrl}`);
      // Log the actual page state for debugging
      const pageTitle = await page.title();
      console.log(`Page title: ${pageTitle}`);
    }
  });
});
