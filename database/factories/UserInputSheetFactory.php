<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Models\UserInputSheet;
use App\Models\User;
use Faker\Generator as Faker;

$factory->define(UserInputSheet::class, function (Faker $faker, array $attributes) {
    $accountJson = [
        '現金' => $faker->numberBetween(100000, ********),
        '売掛金' => $faker->numberBetween(50000, 5000000),
        '在庫' => $faker->numberBetween(20000, 2000000),
    ];

    return [
        'user_id' => $attributes['user_id'] ?? factory(User::class)->create()->id,
        'grouping_name' => $faker->company,
        'data_name' => $faker->words(3, true),
        'data_date' => $faker->dateTimeBetween('-2 years', 'now')->format('Y-m-d'),
        'account_json' => json_encode($accountJson),
        'filtered_account_json' => json_encode($accountJson), // 同じデータを使用
        'constitution_json' => json_encode([
            'assets' => $faker->numberBetween(1000000, ********),
            'liabilities' => $faker->numberBetween(500000, ********),
            'equity' => $faker->numberBetween(500000, ********),
        ]),
    ];
});
