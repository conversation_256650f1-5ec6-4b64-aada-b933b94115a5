<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCapitalizeDebtFromPresidentToUserInputSheets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->boolean('capitalize_debt_from_president')
                ->default(false)
                ->after('data_date')
                ->comment('グラフの役員借入金を資本とする');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->dropColumn('capitalize_debt_from_president');
        });
    }
}
