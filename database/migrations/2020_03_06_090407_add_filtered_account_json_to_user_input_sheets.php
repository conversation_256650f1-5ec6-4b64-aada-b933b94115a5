<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFilteredAccountJsonToUserInputSheets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->json('filtered_account_json')->after('account_json')->comment('表示用科目JSON');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->dropColumn('filtered_account_json');
        });
    }
}
