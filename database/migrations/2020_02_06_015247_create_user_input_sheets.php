<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateUserInputSheets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_input_sheets', function (Blueprint $table) {
            $table->increments('id')->comment('入力表ID');
            $table->integer('user_id')->unsigned()->comment('ユーザーID');
            $table->string('grouping_name')->comment('組み分け名(企業名)');
            $table->string('data_name')->comment('データ名');
            $table->date('data_date')->comment('対象月(データは日付)');
            $table->json('account_json')->comment('科目JSON');
            $table->json('constitution_json')->comment('体質JSON');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');
        });
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE user_input_sheets COMMENT '入力表'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_input_sheets');
    }
}
