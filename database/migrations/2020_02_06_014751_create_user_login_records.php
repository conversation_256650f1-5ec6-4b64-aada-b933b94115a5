<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateUserLoginRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_login_records', function (Blueprint $table) {
            $table->increments('id')->comment('ログイン履歴ID');
            $table->integer('user_id')->unsigned()->comment('ユーザーID');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users');
        });
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE user_login_records COMMENT 'ログイン履歴'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_login_records');
    }
}
