<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->increments('id')->comment('ユーザーID');
            $table->string('company_name')->comment('企業名');
            $table->string('name')->comment('ユーザー名');
            $table->string('email')->unique()->comment('メール');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('account_id')->unique()->comment('アカウントID');
            $table->string('password')->comment('パスワード');
            $table->rememberToken();
            $table->dateTime('start_at')->comment('利用開始日時');
            $table->dateTime('end_at')->comment('利用終了日時');
            $table->tinyInteger('mst_theme_id')->unsigned()->comment('グラフカラー');
            $table->boolean('show_link')->comment('キャッシュ上場リンク有無');
            $table->dateTime('last_login_at')->nullable()->comment('最終ログイン日時');
            $table->dateTime('last_update_at')->nullable()->comment('最終更新日時');
            $table->timestamps();
            $table->softDeletes();
        });
        // MySQL-specific FULLTEXT index, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE users ADD FULLTEXT INDEX (`company_name`, `name`) WITH PARSER ngram");
        }
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE users COMMENT 'ユーザー情報'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
