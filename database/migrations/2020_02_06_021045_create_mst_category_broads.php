<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateMstCategoryBroads extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mst_category_broads', function (Blueprint $table) {
            $table->integer('id')->unsigned()->primary()->comment('大分類ID');
            $table->tinyInteger('category_type')->unsigned()->comment('分類種類(1:貸方、2:借方)');
            $table->string('category_name')->comment('分類名');
            $table->integer('sort_order')->unsigned()->comment('表示順');
            $table->timestamps();
            $table->softDeletes();
        });
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE mst_category_broads COMMENT '大分類'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mst_category_broads');
    }
}
