<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDisableChartToUserInputSheets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->boolean('disable_chart')->default(false)->after('constitution_json')->comment('グラフ非表示');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_input_sheets', function (Blueprint $table) {
            $table->dropColumn('disable_chart');
        });
    }
}
