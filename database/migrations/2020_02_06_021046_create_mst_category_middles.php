<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateMstCategoryMiddles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mst_category_middles', function (Blueprint $table) {
            $table->integer('id')->primary()->comment('中分類ID');
            $table->integer('mst_category_broad_id')->unsigned()->comment('大分類ID');
            $table->string('category_name')->comment('分類名');
            $table->integer('sort_order')->unsigned()->comment('表示順');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mst_category_broad_id')->references('id')->on('mst_category_broads');
        });
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE mst_category_middles COMMENT '中分類'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mst_category_middles');
    }
}
