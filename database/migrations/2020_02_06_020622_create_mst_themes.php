<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateMstThemes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mst_themes', function (Blueprint $table) {
            $table->tinyInteger('id')->unsigned()->primary()->comment('テーマID');
            $table->string('color_1')->comment('色１');
            $table->string('color_2')->comment('色２');
            $table->string('color_3')->comment('色３');
            $table->timestamps();
            $table->softDeletes();
        });
        // MySQL-specific table comment, skip for SQLite testing
        if (DB::connection()->getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE mst_themes COMMENT 'テーマ定義'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mst_themes');
    }
}
