<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MstThemesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 1],
            [
                'color_1' => '#ffffff',
                'color_2' => '#ffffcc',
                'color_3' => '#ccffff',
                'color_4' => '#ff0000',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 2],
            [
                'color_1' => '#ffffff',
                'color_2' => '#d0d1f6',
                'color_3' => '#efb8e3',
                'color_4' => '#790256',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 3],
            [
                'color_1' => '#ffffff',
                'color_2' => '#e2d9ff',
                'color_3' => '#e2ffa7',
                'color_4' => '#556d8a',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 4],
            [
                'color_1' => '#ffffff',
                'color_2' => '#ffdf9e',
                'color_3' => '#b2e5fa',
                'color_4' => '#5c196b',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 5],
            [
                'color_1' => '#ffffff',
                'color_2' => '#daf8e4',
                'color_3' => '#a5e5ba',
                'color_4' => '#3ac569',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 6],
            [
                'color_1' => '#ffffff',
                'color_2' => '#ffe6e6',
                'color_3' => '#ffb6b6',
                'color_4' => '#be9c71',
            ]
        );
        DB::table('mst_themes')->updateOrInsert(
            ['id' => 7],
            [
                'color_1' => '#ffffff',
                'color_2' => '#e6b8b7',
                'color_3' => '#bfbfbf',
                'color_4' => '#ff0000',
            ]
        );
    }
}
