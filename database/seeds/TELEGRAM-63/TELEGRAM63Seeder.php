<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TELEGRAM63Seeder extends Seeder
{
    /**
     * jsonの中の"資本"を"純資産"に置き換える
     *
     * @return void
     */
    public function run()
    {
        $sheets = DB::table('user_input_sheets')->get();
        foreach($sheets as $sheet) {
            DB::table('user_input_sheets')
                ->where('id', $sheet->id)
                ->update([
                    'account_json' => str_replace('"資本"', '"純資産"', $sheet->account_json),
                    'constitution_json' => str_replace('"資本"', '"純資産"', $sheet->constitution_json),
                    'filtered_account_json' => str_replace('"資本"', '"純資産"', $sheet->filtered_account_json),
                ]);
        }
    }
}
