<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MstInputSamplesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('mst_input_samples')->truncate();
        DB::table('mst_input_samples')->insert([
            'id' => 1,
            'account_json' => json_encode([
                'creditTotal' => 0,
                'debitTotal' => 0,
                'credit' => [
                    [
                        'name' => '流動資産',
                        'children' => [
                            [
                                'name' => '売上債権',
                                'constitutionType' => '悪玉資産',
                                'color' => 1,
                                'children' => [
                                    ['name' => '受取手形', 'value' => 0],
                                    ['name' => '売掛金', 'value' => 0],
                                    ['name' => '割引手形', 'value' => 0, 'type' => 'src'],
                                    ['name' => '裏書手形', 'value' => 0, 'type' => 'src'],
                                ]
                            ],
                            [
                                'name' => '棚卸資産',
                                'constitutionType' => '悪玉資産',
                                'color' => 1,
                                'children' => [
                                    ['name' => '棚卸資産', 'value' => 0],
                                    ['name' => '貯蔵品', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => 'その他流動資産',
                                'constitutionType' => '悪玉資産',
                                'color' => 1,
                                'children' => [
                                    ['name' => '前渡金', 'value' => 0],
                                    ['name' => '立替金', 'value' => 0],
                                    ['name' => '仮払金', 'value' => 0],
                                    ['name' => '前払費用', 'value' => 0],
                                    ['name' => '未収入金', 'value' => 0],
                                    ['name' => '仮払消費税等', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => '現金預金',
                                'constitutionType' => '現金等',
                                'children' => [
                                    ['name' => '現金・預金', 'value' => 0],
                                    ['name' => '当座預金', 'value' => 0],
                                    ['name' => '普通預金', 'value' => 0],
                                    ['name' => '定期預金', 'value' => 0],
                                    ['name' => '積立預金', 'value' => 0],
                                    ['name' => '短期貸付金', 'value' => 0],
                                    ['name' => '有価証券', 'value' => 0],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name' => '固定資産',
                        'children' => [
                            [
                                'name' => '投資等その他の資産',
                                'constitutionType' => '脂肪資産',
                                'color' => 2,
                                'children' => [
                                    ['name' => '投資有価証券', 'value' => 0],
                                    ['name' => '保険積立金', 'value' => 0],
                                    ['name' => '長期貸付金', 'value' => 0],
                                    ['name' => '出資金', 'value' => 0],
                                    ['name' => '差入保証金', 'value' => 0],
                                    ['name' => '敷金', 'value' => 0],
                                    ['name' => '預託金', 'value' => 0],
                                    ['name' => '長期前払費用', 'value' => 0],
                                    ['name' => '自己株式', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => '無形固定資産',
                                'constitutionType' => '脂肪資産',
                                'color' => 2,
                                'children' => [
                                    ['name' => 'ソフトウェア', 'value' => 0],
                                    ['name' => '電話加入権', 'value' => 0],
                                    ['name' => '借地権', 'value' => 0],
                                    ['name' => '無形固定資産', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => '有形固定資産',
                                'constitutionType' => '脂肪資産',
                                'color' => 2,
                                'children' => [
                                    ['name' => '建物', 'value' => 0],
                                    ['name' => '建物附属設備', 'value' => 0],
                                    ['name' => '構築物', 'value' => 0],
                                    ['name' => '機械装置', 'value' => 0],
                                    ['name' => '車両運搬具', 'value' => 0],
                                    ['name' => '工具器具備品', 'value' => 0],
                                    ['name' => 'リース資産', 'value' => 0],
                                    ['name' => '建設仮勘定', 'value' => 0],
                                    ['name' => '土地', 'value' => 0],
                                    ['name' => '有形固定資産', 'value' => 0],
                                ]
                            ],
                        ],
                    ],
                    [
                        'name' => '繰延資産',
                        'children' => [
                            [
                                'name' => '繰延資産',
                                'constitutionType' => '脂肪資産',
                                'color' => 2,
                                'children' => [
                                    ['name' => '繰延資産', 'value' => 0],
                                    ['name' => 'その他繰延資産', 'value' => 0],
                                ]
                            ],
                        ],
                    ],
                    [
                        'name' => '利益剰余金',
                        'color' => 3,
                        'children' => [
                            [
                                'name' => '利益剰余金',
                                'type' => 'fixed',
                                'constitutionType' => '利益剰余金',
                                'color' => 3,
                                'children' => [
                                    ['name' => '利益剰余金', 'value' => 0, 'type' => 'fixed'],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name' => 'その他包括利益累計額',
                        'color' => 3,
                        'children' => [
                            [
                                'name' => 'その他包括利益累計額',
                                'type' => 'fixed',
                                'constitutionType' => 'その他包括利益累計額',
                                'color' => 3,
                                'children' => [
                                    ['name' => 'その他包括利益累計額', 'value' => 0, 'type' => 'fixed'],
                                ]
                            ],
                        ]
                    ],
                ],
                'debit' => [
                    [
                        'name' => '流動負債',
                        'children' => [
                            [
                                'name' => '仕入債務',
                                'constitutionType' => '善玉負債',
                                'color' => 1,
                                'children' => [
                                    ['name' => '支払手形', 'value' => 0],
                                    ['name' => '買掛金', 'value' => 0],
                                    ['name' => '裏書手形', 'value' => 0, 'type' => 'dest'],
                                ]
                            ],
                            [
                                'name' => 'その他流動負債',
                                'constitutionType' => '善玉負債',
                                'color' => 1,
                                'children' => [
                                    ['name' => '未払金', 'value' => 0],
                                    ['name' => '未払費用', 'value' => 0],
                                    ['name' => '未払法人税等', 'value' => 0],
                                    ['name' => '未払消費税等', 'value' => 0],
                                    ['name' => '前受金', 'value' => 0],
                                    ['name' => '預り金', 'value' => 0],
                                    ['name' => '仮受金', 'value' => 0],
                                    ['name' => '貸倒引当金', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => '短期',
                                'constitutionType' => '有利子負債',
                                'children' => [
                                    ['name' => '短期借入金', 'value' => 0],
                                    ['name' => '割引手形', 'value' => 0, 'type' => 'dest'],
                                    ['name' => 'リース債務', 'value' => 0],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name' => '固定負債',
                        'children' => [
                            [
                                'name' => '長期',
                                'constitutionType' => '有利子負債',
                                'children' => [
                                    ['name' => '長期借入金', 'value' => 0],
                                    ['name' => '長期未払金', 'value' => 0],
                                    ['name' => 'リース債務', 'value' => 0],
                                ]
                            ],
                            [
                                'name' => 'その他固定負債',
                                'constitutionType' => 'Ｅ資本',
                                'color' => 2,
                                'children' => [
                                    ['name' => '受入保証金', 'value' => 0],
                                    ['name' => '退職給付引当金', 'value' => 0],
                                    ['name' => 'その他引当金', 'value' => 0],
                                    ['name' => '貸倒引当金', 'value' => 0],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name' => '純資産',
                        'children' => [
                            [
                                'name' => '純資産',
                                'constitutionType' => 'Ｅ資本',
                                'color' => 2,
                                'children' => [
                                    ['name' => '資本金', 'value' => 0],
                                    ['name' => '資本剰余金', 'value' => 0],
                                    ['name' => '利益剰余金', 'value' => 0],
                                    ['name' => 'その他包括利益累計額', 'value' => 0],
                                    ['name' => 'その他', 'value' => 0],
                                ]
                            ],
                        ]
                    ],
                ],
            ]),
        ]);
    }
}
