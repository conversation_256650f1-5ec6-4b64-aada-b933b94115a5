<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DevelopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 開発用
        DB::table('administrators')->insert([
            'name' => 'dev',
            'account_id' => 'dev',
            'password' => Hash::make('devtest'),
        ]);
        DB::table('users')->insert([
            'company_name' => 'devtest',
            'user_name' => 'devtestuser',
            'account_id' => 'devuser',
            'password' => Hash::make('password'),
            'start_at' => now(),
            'end_at' => Carbon::parse('+3 day'),
            'mst_theme_id' => 1,
            'show_link' => true,
        ]);
    }
}
