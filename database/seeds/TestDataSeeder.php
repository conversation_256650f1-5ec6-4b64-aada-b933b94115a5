<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds for e2e testing.
     * 
     * Creates test users and admin accounts with predictable credentials
     * for automated testing purposes.
     *
     * @return void
     */
    public function run()
    {
        // Only run in testing environment
        if (app()->environment() !== 'testing') {
            $this->command->warn('TestDataSeeder should only be run in testing environment. Current environment: ' . app()->environment() . '. Skipping...');
            return;
        }

        $this->command->info('Creating test users and administrators for e2e testing...');

        // Create test administrators for admin interface testing
        DB::table('administrators')->updateOrInsert(
            ['account_id' => 'test-admin'],
            [
                'name' => 'Test Administrator',
                'account_id' => 'test-admin',
                'password' => Hash::make('adminpass123'),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Create additional admin for negative testing
        DB::table('administrators')->updateOrInsert(
            ['account_id' => 'test-admin-2'],
            [
                'name' => 'Test Administrator 2',
                'account_id' => 'test-admin-2', 
                'password' => Hash::make('adminpass456'),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Create valid test user (active account)
        DB::table('users')->updateOrInsert(
            ['account_id' => 'test-user'],
            [
                'company_name' => 'Test Company Ltd.',
                'user_name' => 'Test User',
                'account_id' => 'test-user',
                'password' => Hash::make('testpass123'),
                'start_at' => now(),
                'end_at' => Carbon::parse('+30 days'),
                'mst_theme_id' => 1,
                'show_link' => true,
                'show_pdf_company_name' => true,
                'max_input_sheets' => 10,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Create expired test user (for testing expired account scenarios)
        DB::table('users')->updateOrInsert(
            ['account_id' => 'test-expired'],
            [
                'company_name' => 'Expired Test Company',
                'user_name' => 'Expired Test User',
                'account_id' => 'test-expired',
                'password' => Hash::make('testpass123'),
                'start_at' => Carbon::parse('-30 days'),
                'end_at' => Carbon::parse('-1 day'),
                'mst_theme_id' => 2,
                'show_link' => false,
                'show_pdf_company_name' => false,
                'max_input_sheets' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Create future test user (for testing not-yet-active accounts)
        DB::table('users')->updateOrInsert(
            ['account_id' => 'test-future'],
            [
                'company_name' => 'Future Test Company',
                'user_name' => 'Future Test User',
                'account_id' => 'test-future',
                'password' => Hash::make('testpass123'),
                'start_at' => Carbon::parse('+5 days'),
                'end_at' => Carbon::parse('+35 days'),
                'mst_theme_id' => 3,
                'show_link' => true,
                'show_pdf_company_name' => true,
                'max_input_sheets' => 15,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Create second valid test user (for testing multiple users)
        DB::table('users')->updateOrInsert(
            ['account_id' => 'test-user-2'],
            [
                'company_name' => 'Test Company 2 Corp.',
                'user_name' => 'Test User 2',
                'account_id' => 'test-user-2',
                'password' => Hash::make('testpass456'),
                'start_at' => now(),
                'end_at' => Carbon::parse('+60 days'),
                'mst_theme_id' => 4,
                'show_link' => false,
                'show_pdf_company_name' => true,
                'max_input_sheets' => 20,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        $this->command->info('Test data seeding completed:');
        $this->command->line('  Administrators:');
        $this->command->line('    - test-admin / adminpass123 (admin interface testing)');
        $this->command->line('    - test-admin-2 / adminpass456 (additional admin testing)');
        $this->command->line('  Users:');
        $this->command->line('    - test-user / testpass123 (valid, active account)');
        $this->command->line('    - test-expired / testpass123 (expired account)');
        $this->command->line('    - test-future / testpass123 (future activation)');
        $this->command->line('    - test-user-2 / testpass456 (valid, different theme)');
    }
}