<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TELEGRAM69Seeder extends Seeder
{
    /**
     * account_jsonの中の"利益剰余金"の次に"その他包括利益累計額"を追加する
     *
     * @return void
     */
    public function run()
    {
        $sheets = DB::table('user_input_sheets')->get();
        foreach ($sheets as $sheet) {
            $account_json = json_decode($sheet->account_json, true);
            $account_json['credit'][] = [
                'name' => 'その他包括利益累計額',
                'color' => 3,
                'children' => [
                    [
                        'name' => 'その他包括利益累計額',
                        'type' => 'fixed',
                        'constitutionType' => 'その他包括利益累計額',
                        'color' => 3,
                        'children' => [
                            ['name' => 'その他包括利益累計額', 'value' => 0, 'type' => 'fixed'],
                        ]
                    ],
                ]
            ];
            DB::table('user_input_sheets')
                ->where('id', $sheet->id)
                ->update([
                    'account_json' => json_encode($account_json),
                ]);
        }
    }
}
