<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TELEGRAM56Seeder extends Seeder
{
    /**
     * account_jsonの中の"繰越利益剰余金"を"利益剰余金"に変更する
     *
     * @return void
     */
    public function run()
    {
        $sheets = DB::table('user_input_sheets')->get();
        foreach ($sheets as $sheet) {
            DB::table('user_input_sheets')
                ->where('id', $sheet->id)
                ->update([
                    'account_json' => preg_replace(
                        '/({.*?)"繰越利益剰余金"(.*?"fixed".*?})/',
                        '${1}"利益剰余金"${2}',
                        $sheet->account_json
                    ),
                ]);
        }
    }
}
