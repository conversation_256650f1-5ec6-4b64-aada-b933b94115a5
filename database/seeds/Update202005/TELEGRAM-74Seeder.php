<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TELEGRAM74Seeder extends Seeder
{
    /**
     * account_jsonの中の"割引手形"と"裏書手形"を連動項目に変換する
     *
     * @return void
     */
    public function run()
    {
        $sheets = DB::table('user_input_sheets')->get();
        foreach ($sheets as $sheet) {
            // echo preg_replace(
            //     [
            //         '/({.*?"売上債権".*?{.*?"割引手形".*?)(}.*?})/',
            //         '/({.*?"売上債権".*?{.*?"裏書手形".*?)(}.*?})/',
            //         '/({.*?"短期".*?{.*?"割引手形".*?)(}.*?})/',
            //         '/({.*?"仕入債務".*?{.*?"裏書手形".*?)(}.*?})/',
            //     ],
            //     [
            //         '${1}, "type": "src"${2}',
            //         '${1}, "type": "src"${2}',
            //         '${1}, "type": "dest"${2}',
            //         '${1}, "type": "dest"${2}',
            //     ],
            //     $sheet->account_json
            // ), "\n";
            // // return;
            DB::table('user_input_sheets')
                ->where('id', $sheet->id)
                ->update([
                    'account_json' => preg_replace(
                        [
                            '/({.*?"売上債権".*?{.*?"割引手形".*?)(}.*?})/',
                            '/({.*?"売上債権".*?{.*?"裏書手形".*?)(}.*?})/',
                            '/({.*?"短期".*?{.*?"割引手形".*?)(}.*?})/',
                            '/({.*?"仕入債務".*?{.*?"裏書手形".*?)(}.*?})/',
                        ],
                        [
                            '${1}, "type": "src"${2}',
                            '${1}, "type": "src"${2}',
                            '${1}, "type": "dest"${2}',
                            '${1}, "type": "dest"${2}',
                        ],
                        $sheet->account_json
                    ),
                ]);
        }
    }
}
