runtime: php72

service: prod

env_variables:
  APP_NAME: ""
  APP_KEY: XXXXXXXXXXXXXXXXXX
  APP_STORAGE: /tmp
  APP_ENV: production
  APP_DEBUG: false
  VIEW_COMPILED_PATH: /tmp
  CACHE_DRIVER: database
  SESSION_DRIVER: database
  SESSION_LIFETIME: 120
  MIX_APP_REPOSITORY: 'api'

  DB_DATABASE: tgmprod_01
  DB_READ_USERNAME: XXXXXXXXXXXXXXXXXX
  DB_READ_PASSWORD: XXXXXXXXXXXXXXXXXX
  DB_WRITE_USERNAME: XXXXXXXXXXXXXXXXXX
  DB_WRITE_PASSWORD: XXXXXXXXXXXXXXXXXX
  DB_HOST: ***********

  LOG_CHANNEL: stackdriver

  ALLOW_IP_ADDRESS_WEB: ""
  ALLOW_IP_ADDRESS_MANAGE: "**************,**************,**************"

vpc_access_connector:
  name: "projects/telegram-267115/locations/asia-northeast1/connectors/telegram-vpc-connector01"

instance_class: B2
basic_scaling:
  max_instances: 1
  idle_timeout: 10m

# 静的ファイル
handlers:
  - url: /css
    static_dir: public/css

  - url: /js
    static_dir: public/js

  - url: /fonts
    static_dir: public/fonts

  - url: /images
    static_dir: public/images

  - url: /.*
    secure: always
    redirect_http_response_code: 301
    script: auto
