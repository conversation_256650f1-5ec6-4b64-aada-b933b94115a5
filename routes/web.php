<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Auth::routes(['confirm' => false, 'reset' => false, 'verify' => false, 'register' => false]);
Route::middleware(['auth:web', 'auth.term', 'cache.nostore'])->namespace('Web')->group(function () {
    Route::view('/', 'web.home');
    Route::get('/init', 'HomeController');
    Route::resource('sheets', 'SheetController')->only([
        'index', 'store', 'update', 'destroy'
    ]);
    Route::post('/pdf/constitution', 'PdfController@constitution');
    Route::post('/pdf/transition', 'PdfController@transition');
});

Route::prefix('manage')->namespace('Manage')->name('manage.')->group(function () {
    Auth::routes(['confirm' => false, 'reset' => false, 'verify' => false, 'register' => false]);
    Route::middleware(['auth:manage'])->group(function () {
        Route::get('/', 'ManageController@index');
        Route::post('/', 'ManageController@entry');
        Route::view('/history/{user}', 'manage.history');
        Route::get('/history/{user}/init', 'HistoryController@index');
        Route::post('/pdf/constitution', 'ManagePdfController@constitution');
        Route::post('/pdf/transition', 'ManagePdfController@transition');
    });
});
