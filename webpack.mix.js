const mix = require("laravel-mix");

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
// mix.babelConfig({
//   presets: [["@babel/preset-env", { modules: false }]],
//   plugins: [
//     [
//       "component",
//       {
//         libraryName: "element-ui",
//         styleLibraryName: "theme-chalk"
//       }
//     ]
//   ]
// });

mix
  .js("resources/js/app.js", "public/js")
  .js("resources/js/manage.js", "public/js")
  .js("resources/js/manage_history.js", "public/js")
  .js("resources/js/login.js", "public/js")
  .extract(["vue"])
  .sass("resources/sass/app.scss", "public/css");

if (mix.inProduction()) {
  mix.version();
} else {
  mix.sourceMaps();
}
