{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js && npm run build", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js && npm run build", "build": "rollup -c", "clean": "rimraf public/js public/css public/mix-manifest.json dist"}, "devDependencies": {"@babel/preset-env": "^7.8.4", "@rollup/plugin-node-resolve": "^7.1.1", "axios": "^0.19", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "cross-env": "^7.0", "d3": "^5.15.0", "element-ui": "^2.13.0", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.1.2", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "moment": "^2.24.0", "resolve-url-loader": "^3.1.0", "rimraf": "^3.0.2", "rollup": "^1.31.1", "rollup-plugin-babel": "^4.3.3", "sass": "^1.15.2", "sass-loader": "^8.0.0", "vue": "^2.6.11", "vue-template-compiler": "^2.6.11", "vuex": "^3.4.0"}, "version": "2.0.30"}