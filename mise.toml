[tools]

[vars]
project = "telegram-458202"
service_account = "tgm-cloudrun-sa@{{vars.project}}.iam.gserviceaccount.com"
max_instances = "5"
cloud_sql_proxy_bin = "cloud-sql-proxy"

[tasks.deploy]
description = "Deploy to Google Cloud Run"
run = """
    gcloud run deploy tgm-app \
    --image asia-northeast1-docker.pkg.dev/{{vars.project}}/tgm-repo/tgm-app:latest \
    --region asia-northeast1 \
    --platform managed \
    --allow-unauthenticated \
    --service-account {{vars.service_account}} \
    --memory 512Mi \
    --cpu 1 \
    --max-instances {{vars.max_instances}} \
    --timeout 300 \
    --port 8080 \
    --env-vars-file=.env.yaml
"""

[tasks."run:sqlauthproxy"]
description = "Start Cloud SQL Auth Proxy"
run = "{{vars.cloud_sql_proxy_bin}} {{vars.project}}:asia-northeast1:tgm-mysql"
